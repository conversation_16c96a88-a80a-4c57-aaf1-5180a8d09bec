const fs = require('fs');
const path = require('path');

/**
 * 验证站点地图文件是否成功生成
 */
function testSitemapGeneration() {
  console.log('开始验证站点地图生成...');
  
  const publicDir = path.join(process.cwd(), 'public');
  const sitemapPath = path.join(publicDir, 'sitemap.xml');
  const robotsPath = path.join(publicDir, 'robots.txt');
  
  // 检查sitemap.xml是否存在
  if (fs.existsSync(sitemapPath)) {
    console.log('✅ sitemap.xml 已成功生成');
    
    // 检查内容
    const sitemapContent = fs.readFileSync(sitemapPath, 'utf8');
    if (sitemapContent.includes('<sitemapindex') && 
        sitemapContent.includes('sitemap-0.xml')) {
      console.log('✅ sitemap.xml 索引文件格式正确');
    } else {
      console.error('❌ sitemap.xml 索引文件格式不正确');
    }
  } else {
    console.error('❌ sitemap.xml 未生成');
  }
  
  // 检查robots.txt是否存在
  if (fs.existsSync(robotsPath)) {
    console.log('✅ robots.txt 已成功生成');
    
    // 检查内容
    const robotsContent = fs.readFileSync(robotsPath, 'utf8');
    if (robotsContent.includes('Sitemap:') && robotsContent.includes('sitemap.xml')) {
      console.log('✅ robots.txt 内容正确，包含站点地图引用');
    } else {
      console.error('❌ robots.txt 内容不正确，缺少站点地图引用');
    }
  } else {
    console.error('❌ robots.txt 未生成');
  }
  
  // 检查是否有sitemap索引文件并验证内容
  const sitemapIndexFiles = fs.readdirSync(publicDir)
    .filter(file => file.startsWith('sitemap-') && file.endsWith('.xml'));
  
  if (sitemapIndexFiles.length > 0) {
    console.log(`✅ 找到 ${sitemapIndexFiles.length} 个站点地图索引文件`);
    
    for (const file of sitemapIndexFiles) {
      console.log(`  - 验证文件: ${file}`);
      const filePath = path.join(publicDir, file);
      const xmlContent = fs.readFileSync(filePath, 'utf8');
      
      // 简单验证包含URL
      if (xmlContent.includes('<url>') && xmlContent.includes('<loc>')) {
        console.log(`    ✅ ${file} 包含URL条目`);
        
        // 验证多语言路径
        const hasEN = xmlContent.includes('/en/') || xmlContent.includes('/en<');
        const hasZH = xmlContent.includes('/zh/') || xmlContent.includes('/zh<');
        
        if (hasEN) {
          console.log('    ✅ 包含英文路径');
        } else {
          console.log('    ⚠️ 未找到英文路径');
        }
        
        if (hasZH) {
          console.log('    ✅ 包含中文路径');
        } else {
          console.log('    ⚠️ 未找到中文路径');
        }
      } else {
        console.error(`    ❌ ${file} 没有包含任何URL条目`);
      }
    }
  } else {
    console.error('❌ 未找到站点地图索引文件');
  }
  
  console.log('\n站点地图验证完成!');
}

// 执行测试
testSitemapGeneration(); 