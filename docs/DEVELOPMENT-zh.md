# 开发指南

本指南提供了设置和开发 AISTAK 项目的详细说明。

## 环境要求

开始开发之前，请确保已安装以下软件：

### 必需软件
- **Node.js** 18.x 或更高版本 ([下载](https://nodejs.org/))
- **pnpm** 8.x 或更高版本 ([安装指南](https://pnpm.io/installation))
- **PostgreSQL** 13.x 或更高版本 ([下载](https://www.postgresql.org/download/))
- **Git** ([下载](https://git-scm.com/downloads))

### 可选工具
- **Docker** 和 **Docker Compose** 用于容器化开发
- **VS Code** 配合推荐扩展（参见 [.vscode/extensions.json](.vscode/extensions.json)）
- **Prisma Studio** 用于数据库管理（包含在 Prisma 中）

## 开发环境设置

### 1. 克隆和安装

```bash
# 克隆仓库
git clone https://github.com/wenhaofree/aistak.git
cd aistak

# 安装依赖
pnpm install

# 复制环境模板
cp .env.example .env.local
```

### 2. 数据库设置

#### 选项 A：本地 PostgreSQL

```bash
# 创建数据库和用户
createdb aistak
createuser aistak_user -P  # 系统会提示输入密码

# 授予权限
psql -d aistak -c "GRANT ALL PRIVILEGES ON DATABASE aistak TO aistak_user;"
psql -d aistak -c "GRANT ALL ON SCHEMA public TO aistak_user;"
```

#### 选项 B：Docker PostgreSQL

```bash
# 启动 PostgreSQL 容器
docker-compose up -d postgres-test

# 数据库将在以下地址可用：
# 主机: localhost
# 端口: 5433
# 数据库: testdb
# 用户: prisma
# 密码: prisma
```

### 3. 环境配置

编辑 `.env.local` 文件：

```env
# 数据库
DATABASE_URL="postgresql://aistak_user:password@localhost:5432/aistak"

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-development-secret
AUTH_SECRET=your-auth-secret

# OAuth（可选 - 用于测试认证）
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

# Stripe（可选 - 用于测试支付）
STRIPE_PRIVATE_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-publishable-key

# 开发环境
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 4. 数据库初始化

```bash
# 生成 Prisma 客户端
pnpm db:generate

# 推送模式到数据库
pnpm db:push

# 可选：使用示例数据填充数据库
pnpm db:seed  # 如果存在种子脚本
```

### 5. 启动开发服务器

```bash
# 使用 Turbopack 启动开发服务器
pnpm dev

# 应用将在以下地址可用：
# http://localhost:3000
```

## 开发工作流程

### 代码结构

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # 国际化路由
│   ├── api/               # API 路由
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── sections/         # 页面区块
│   └── blocks/           # 内容块
├── lib/                  # 工具函数
├── types/                # TypeScript 定义
├── constants/            # 应用常量
└── i18n/                 # 国际化
```

### 开发命令

```bash
# 开发
pnpm dev              # 使用 Turbopack 启动开发服务器
pnpm dev:debug        # 启用调试模式启动
pnpm build            # 构建生产版本
pnpm start            # 启动生产服务器
pnpm lint             # 运行 ESLint
pnpm lint:fix         # 修复 ESLint 问题

# 数据库
pnpm db:push          # 推送模式变更
pnpm db:pull          # 从数据库拉取模式
pnpm db:generate      # 生成 Prisma 客户端
pnpm db:studio        # 打开 Prisma Studio
pnpm db:reset         # 重置数据库（破坏性操作）
pnpm db:migrate       # 运行迁移

# 测试
pnpm test             # 运行所有测试
pnpm test:watch       # 监视模式运行测试
pnpm test:db          # 运行数据库测试
pnpm test:e2e         # 运行端到端测试

# 类型检查
pnpm type-check       # 运行 TypeScript 编译器
```

### 代码风格和检查

项目使用 ESLint 和 Prettier 进行代码格式化：

```bash
# 检查代码风格
pnpm lint

# 修复可自动修复的问题
pnpm lint:fix

# 使用 Prettier 格式化代码
pnpm format
```

### Git 工作流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **进行更改**
   - 遵循现有代码风格
   - 为新功能添加测试
   - 更新文档

3. **提交更改**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **推送并创建 PR**
   ```bash
   git push origin feature/your-feature-name
   ```

### 提交消息约定

我们使用 [约定式提交](https://www.conventionalcommits.org/)：

- `feat:` - 新功能
- `fix:` - 错误修复
- `docs:` - 文档更改
- `style:` - 代码风格更改（格式化等）
- `refactor:` - 代码重构
- `test:` - 添加或更新测试
- `chore:` - 维护任务

## 数据库开发

### 模式更改

1. **修改 Prisma 模式** (`prisma/schema.prisma`)
2. **推送更改到数据库**
   ```bash
   pnpm db:push
   ```
3. **生成新客户端**
   ```bash
   pnpm db:generate
   ```

### 迁移（生产环境）

对于生产部署，使用迁移：

```bash
# 创建迁移
npx prisma migrate dev --name your-migration-name

# 部署迁移
npx prisma migrate deploy
```

### 数据库种子

在 `prisma/seed.ts` 中创建种子脚本：

```typescript
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // 在这里添加种子数据
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
```

## 测试

### 单元测试

```bash
# 运行单元测试
pnpm test

# 运行覆盖率测试
pnpm test:coverage

# 运行特定测试文件
pnpm test src/components/ToolCard.test.tsx
```

### 数据库测试

```bash
# 设置测试数据库
pnpm test:db:setup

# 运行数据库测试
pnpm test:db

# 使用 Docker 运行
pnpm test:db:docker
```

### 端到端测试

```bash
# 运行 E2E 测试
pnpm test:e2e

# 在有头模式下运行 E2E 测试
pnpm test:e2e:headed
```

## 国际化

### 添加新语言

1. **创建翻译文件**
   ```bash
   touch messages/fr.json  # 法语
   ```

2. **更新路由配置**
   ```typescript
   // src/i18n/routing.ts
   export const locales = ['en', 'zh', 'fr'] as const;
   ```

3. **添加翻译**
   ```json
   {
     "tools": {
       "title": "Outils IA",
       "description": "Découvrez les meilleurs outils IA"
     }
   }
   ```

### 使用翻译

```typescript
import { useTranslations } from 'next-intl';

function MyComponent() {
  const t = useTranslations('tools');
  
  return <h1>{t('title')}</h1>;
}
```

## 性能优化

### 缓存策略

- **API 路由**：实现适当 TTL 的缓存
- **静态资源**：使用 Next.js 自动优化
- **数据库查询**：使用 Prisma 查询优化
- **图片**：利用 Next.js Image 组件

### 监控

```bash
# 分析包大小
pnpm analyze

# 检查性能
pnpm lighthouse

# 监控构建时间
pnpm build --profile
```

## 调试

### 开发调试

1. **启用调试模式**
   ```env
   DEBUG=true
   NODE_ENV=development
   ```

2. **使用浏览器开发工具**
   - React Developer Tools
   - Next.js DevTools

3. **数据库调试**
   ```bash
   pnpm db:studio  # 可视化数据库浏览器
   ```

### 生产调试

1. **检查日志**
   ```bash
   # 应用日志
   tail -f logs/app.log
   
   # 数据库日志
   tail -f logs/db.log
   ```

2. **性能监控**
   - 使用 Vercel Analytics（如果部署在 Vercel 上）
   - 实现自定义监控

## 故障排除

### 常见问题

1. **数据库连接错误**
   - 检查 PostgreSQL 是否运行
   - 验证 .env.local 中的 DATABASE_URL
   - 确保数据库存在且用户有权限

2. **构建错误**
   - 清除 Next.js 缓存：`rm -rf .next`
   - 重新安装依赖：`rm -rf node_modules && pnpm install`
   - 检查 TypeScript 错误：`pnpm type-check`

3. **认证问题**
   - 验证 OAuth 应用配置
   - 检查 NEXTAUTH_URL 是否匹配域名
   - 确保设置了 NEXTAUTH_SECRET

4. **Prisma 问题**
   - 重新生成客户端：`pnpm db:generate`
   - 重置数据库：`pnpm db:reset`（破坏性操作）
   - 检查模式语法

### 获取帮助

- 查看 [GitHub Issues](https://github.com/wenhaofree/aistak/issues)
- 查阅 [API 文档](./API.md)
- 联系维护者

## 贡献

详细的贡献指南请参见 [CONTRIBUTING.md](./CONTRIBUTING.md)。
