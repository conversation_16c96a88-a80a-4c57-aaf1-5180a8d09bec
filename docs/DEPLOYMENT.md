# Deployment Guide

This guide covers various deployment options for the AISTAK application.

## Overview

AISTAK is built with Next.js 15 and can be deployed on various platforms. The application requires:

- Node.js 18+ runtime
- PostgreSQL database
- Environment variables configuration
- Static file hosting (for images and assets)

## Deployment Platforms

### 1. Vercel (Recommended)

Vercel provides the best experience for Next.js applications with automatic deployments and optimizations.

#### Prerequisites
- Vercel account
- GitHub repository
- PostgreSQL database (Vercel Postgres or external)

#### Setup Steps

1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Login and deploy
   vercel login
   vercel --prod
   ```

2. **Configure Environment Variables**
   
   In Vercel Dashboard → Project Settings → Environment Variables:
   
   ```env
   # Database
   DATABASE_URL=postgresql://user:password@host:port/database
   
   # Authentication
   NEXTAUTH_URL=https://your-domain.vercel.app
   NEXTAUTH_SECRET=your-production-secret
   AUTH_SECRET=your-auth-secret
   
   # OAuth
   AUTH_GOOGLE_ID=your-google-client-id
   AUTH_GOOGLE_SECRET=your-google-client-secret
   AUTH_GITHUB_ID=your-github-client-id
   AUTH_GITHUB_SECRET=your-github-client-secret
   
   # Stripe
   STRIPE_PRIVATE_KEY=sk_live_your-stripe-secret
   STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your-publishable-key
   
   # Other
   NODE_ENV=production
   ```

3. **Database Setup**
   ```bash
   # If using Vercel Postgres
   vercel postgres create
   
   # Run migrations
   npx prisma migrate deploy
   ```

4. **Configure Domains**
   - Add custom domain in Vercel Dashboard
   - Update NEXTAUTH_URL to match your domain

#### Vercel Configuration

Create `vercel.json`:

```json
{
  "buildCommand": "pnpm build",
  "outputDirectory": ".next",
  "installCommand": "pnpm install",
  "framework": "nextjs",
  "regions": ["iad1"],
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "crons": [
    {
      "path": "/api/cron/cleanup",
      "schedule": "0 2 * * *"
    }
  ]
}
```

### 2. Docker Deployment

#### Dockerfile

```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json pnpm-lock.yaml* ./
RUN corepack enable pnpm && pnpm i --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN npx prisma generate

# Build application
RUN corepack enable pnpm && pnpm build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

#### Docker Compose

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/aistak
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-secret
    depends_on:
      - db
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: aistak
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

#### Deployment Commands

```bash
# Build and start services
docker-compose up -d

# Run database migrations
docker-compose exec app npx prisma migrate deploy

# View logs
docker-compose logs -f app

# Scale application
docker-compose up -d --scale app=3
```

### 3. AWS Deployment

#### Using AWS App Runner

1. **Create apprunner.yaml**
   ```yaml
   version: 1.0
   runtime: nodejs18
   build:
     commands:
       build:
         - npm install -g pnpm
         - pnpm install
         - npx prisma generate
         - pnpm build
   run:
     runtime-version: 18
     command: pnpm start
     network:
       port: 3000
       env: PORT
   ```

2. **Environment Variables**
   Configure in AWS App Runner console or via AWS CLI

3. **Database**
   Use AWS RDS PostgreSQL instance

#### Using AWS ECS with Fargate

1. **Build and push Docker image**
   ```bash
   # Build image
   docker build -t aistak .
   
   # Tag for ECR
   docker tag aistak:latest 123456789012.dkr.ecr.region.amazonaws.com/aistak:latest
   
   # Push to ECR
   docker push 123456789012.dkr.ecr.region.amazonaws.com/aistak:latest
   ```

2. **Create ECS task definition**
3. **Configure load balancer and auto-scaling**

### 4. Railway

Railway provides a simple deployment experience with automatic builds.

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway link
railway up
```

Configure environment variables in Railway dashboard.

### 5. DigitalOcean App Platform

1. **Connect GitHub repository**
2. **Configure build settings**
   - Build command: `pnpm build`
   - Run command: `pnpm start`
3. **Add environment variables**
4. **Configure database component**

## Database Deployment

### PostgreSQL Options

1. **Vercel Postgres** (Recommended for Vercel deployments)
2. **AWS RDS**
3. **DigitalOcean Managed Databases**
4. **Google Cloud SQL**
5. **Supabase**
6. **PlanetScale** (MySQL alternative)

### Database Migration

```bash
# Production migration
npx prisma migrate deploy

# Seed production database (if needed)
npx prisma db seed
```

## Environment Configuration

### Production Environment Variables

```env
# Core
NODE_ENV=production
NEXTAUTH_URL=https://your-domain.com
DATABASE_URL=postgresql://user:password@host:port/database

# Security
NEXTAUTH_SECRET=your-very-secure-secret-key
AUTH_SECRET=your-auth-secret-key

# OAuth
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

# Stripe
STRIPE_PRIVATE_KEY=sk_live_your-stripe-secret
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your-publishable-key

# Optional
NEXT_PUBLIC_API_URL=https://your-domain.com/api
```

### Security Considerations

1. **Use strong secrets**
   ```bash
   # Generate secure secrets
   openssl rand -base64 32
   ```

2. **Enable HTTPS**
   - Use SSL certificates
   - Configure HSTS headers
   - Set secure cookie flags

3. **Database security**
   - Use connection pooling
   - Enable SSL connections
   - Restrict database access

4. **Environment isolation**
   - Separate staging and production
   - Use different API keys
   - Implement proper access controls

## Performance Optimization

### Build Optimization

```javascript
// next.config.mjs
const nextConfig = {
  output: 'standalone',
  compress: true,
  poweredByHeader: false,
  generateEtags: true,
  
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
  },
  
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
};
```

### CDN Configuration

1. **Static assets**
   - Use CDN for images and static files
   - Configure proper cache headers
   - Enable compression

2. **API caching**
   - Implement Redis for API caching
   - Use appropriate cache-control headers
   - Configure edge caching

### Database Optimization

1. **Connection pooling**
   ```env
   DATABASE_URL="postgresql://user:password@host:port/database?connection_limit=10&pool_timeout=20"
   ```

2. **Query optimization**
   - Use database indexes
   - Implement query caching
   - Monitor slow queries

## Monitoring and Logging

### Application Monitoring

1. **Error tracking**
   - Sentry integration
   - Custom error boundaries
   - API error logging

2. **Performance monitoring**
   - Web Vitals tracking
   - API response times
   - Database query performance

3. **Uptime monitoring**
   - Health check endpoints
   - External monitoring services
   - Alerting configuration

### Logging

```typescript
// lib/logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.json(),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
  ],
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

## Backup and Recovery

### Database Backups

```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
```

### Application Backups

1. **Code repository** - GitHub/GitLab
2. **Environment variables** - Secure storage
3. **Static assets** - CDN/S3 backup
4. **Database** - Regular automated backups

## Troubleshooting

### Common Deployment Issues

1. **Build failures**
   - Check Node.js version compatibility
   - Verify environment variables
   - Review build logs

2. **Database connection issues**
   - Verify connection string
   - Check network connectivity
   - Ensure database is running

3. **Authentication problems**
   - Verify OAuth app configuration
   - Check redirect URLs
   - Validate environment variables

4. **Performance issues**
   - Monitor resource usage
   - Check database queries
   - Review caching configuration

### Health Checks

Create health check endpoints:

```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    // Check database connection
    await prisma.$queryRaw`SELECT 1`;
    
    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    });
  } catch (error) {
    return NextResponse.json(
      { status: 'unhealthy', error: error.message },
      { status: 500 }
    );
  }
}
```

## Rollback Strategy

1. **Version tagging**
   ```bash
   git tag -a v1.0.0 -m "Release version 1.0.0"
   git push origin v1.0.0
   ```

2. **Database migrations**
   - Keep migration rollback scripts
   - Test rollback procedures
   - Backup before major changes

3. **Deployment rollback**
   - Use platform-specific rollback features
   - Maintain previous deployment artifacts
   - Document rollback procedures
