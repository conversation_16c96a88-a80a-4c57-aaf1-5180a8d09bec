# Development Guide

This guide provides detailed instructions for setting up and developing the AISTAK project.

## Prerequisites

Before starting development, ensure you have the following installed:

### Required Software
- **Node.js** 18.x or higher ([Download](https://nodejs.org/))
- **pnpm** 8.x or higher ([Installation Guide](https://pnpm.io/installation))
- **PostgreSQL** 13.x or higher ([Download](https://www.postgresql.org/download/))
- **Git** ([Download](https://git-scm.com/downloads))

### Optional Tools
- **Docker** and **Docker Compose** for containerized development
- **VS Code** with recommended extensions (see [.vscode/extensions.json](.vscode/extensions.json))
- **Prisma Studio** for database management (included with Prisma)

## Development Environment Setup

### 1. Clone and Install

```bash
# Clone the repository
git clone https://github.com/wenhaofree/aistak.git
cd aistak

# Install dependencies
pnpm install

# Copy environment template
cp .env.example .env.local
```

### 2. Database Setup

#### Option A: Local PostgreSQL

```bash
# Create database and user
createdb aistak
createuser aistak_user -P  # You'll be prompted for a password

# Grant privileges
psql -d aistak -c "GRANT ALL PRIVILEGES ON DATABASE aistak TO aistak_user;"
psql -d aistak -c "GRANT ALL ON SCHEMA public TO aistak_user;"
```

#### Option B: Docker PostgreSQL

```bash
# Start PostgreSQL container
docker-compose up -d postgres-test

# The database will be available at:
# Host: localhost
# Port: 5433
# Database: testdb
# User: prisma
# Password: prisma
```

### 3. Environment Configuration

Edit `.env.local` with your configuration:

```env
# Database
DATABASE_URL="postgresql://aistak_user:password@localhost:5432/aistak"

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-development-secret
AUTH_SECRET=your-auth-secret

# OAuth (Optional - for testing authentication)
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret

NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret

# Stripe (Optional - for testing payments)
STRIPE_PRIVATE_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your-publishable-key

# Development
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 4. Database Initialization

```bash
# Generate Prisma client
pnpm db:generate

# Push schema to database
pnpm db:push

# Optional: Seed database with sample data
pnpm db:seed  # If seed script exists
```

### 5. Start Development Server

```bash
# Start development server with Turbopack
pnpm dev

# The application will be available at:
# http://localhost:3000
```

## Development Workflow

### Code Structure

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # Internationalized routes
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # React components
│   ├── ui/               # Base UI components
│   ├── sections/         # Page sections
│   └── blocks/           # Content blocks
├── lib/                  # Utility functions
├── types/                # TypeScript definitions
├── constants/            # Application constants
└── i18n/                 # Internationalization
```

### Development Commands

```bash
# Development
pnpm dev              # Start dev server with Turbopack
pnpm dev:debug        # Start with debugging enabled
pnpm build            # Build for production
pnpm start            # Start production server
pnpm lint             # Run ESLint
pnpm lint:fix         # Fix ESLint issues

# Database
pnpm db:push          # Push schema changes
pnpm db:pull          # Pull schema from database
pnpm db:generate      # Generate Prisma client
pnpm db:studio        # Open Prisma Studio
pnpm db:reset         # Reset database (destructive)
pnpm db:migrate       # Run migrations

# Testing
pnpm test             # Run all tests
pnpm test:watch       # Run tests in watch mode
pnpm test:db          # Run database tests
pnpm test:e2e         # Run end-to-end tests

# Type checking
pnpm type-check       # Run TypeScript compiler
```

### Code Style and Linting

The project uses ESLint and Prettier for code formatting:

```bash
# Check code style
pnpm lint

# Fix auto-fixable issues
pnpm lint:fix

# Format code with Prettier
pnpm format
```

### Git Workflow

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the existing code style
   - Add tests for new features
   - Update documentation

3. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **Push and create PR**
   ```bash
   git push origin feature/your-feature-name
   ```

### Commit Message Convention

We use [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

## Database Development

### Schema Changes

1. **Modify the Prisma schema** (`prisma/schema.prisma`)
2. **Push changes to database**
   ```bash
   pnpm db:push
   ```
3. **Generate new client**
   ```bash
   pnpm db:generate
   ```

### Migrations (Production)

For production deployments, use migrations:

```bash
# Create migration
npx prisma migrate dev --name your-migration-name

# Deploy migrations
npx prisma migrate deploy
```

### Database Seeding

Create seed scripts in `prisma/seed.ts`:

```typescript
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Your seed data here
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
```

## Testing

### Unit Tests

```bash
# Run unit tests
pnpm test

# Run with coverage
pnpm test:coverage

# Run specific test file
pnpm test src/components/ToolCard.test.tsx
```

### Database Tests

```bash
# Setup test database
pnpm test:db:setup

# Run database tests
pnpm test:db

# Run with Docker
pnpm test:db:docker
```

### End-to-End Tests

```bash
# Run E2E tests
pnpm test:e2e

# Run E2E tests in headed mode
pnpm test:e2e:headed
```

## Internationalization

### Adding New Languages

1. **Create translation file**
   ```bash
   touch messages/fr.json  # For French
   ```

2. **Update routing configuration**
   ```typescript
   // src/i18n/routing.ts
   export const locales = ['en', 'zh', 'fr'] as const;
   ```

3. **Add translations**
   ```json
   {
     "tools": {
       "title": "Outils IA",
       "description": "Découvrez les meilleurs outils IA"
     }
   }
   ```

### Using Translations

```typescript
import { useTranslations } from 'next-intl';

function MyComponent() {
  const t = useTranslations('tools');
  
  return <h1>{t('title')}</h1>;
}
```

## Performance Optimization

### Caching Strategy

- **API Routes**: Implement caching with appropriate TTL
- **Static Assets**: Use Next.js automatic optimization
- **Database Queries**: Use Prisma query optimization
- **Images**: Leverage Next.js Image component

### Monitoring

```bash
# Analyze bundle size
pnpm analyze

# Check performance
pnpm lighthouse

# Monitor build times
pnpm build --profile
```

## Debugging

### Development Debugging

1. **Enable debug mode**
   ```env
   DEBUG=true
   NODE_ENV=development
   ```

2. **Use browser dev tools**
   - React Developer Tools
   - Next.js DevTools

3. **Database debugging**
   ```bash
   pnpm db:studio  # Visual database browser
   ```

### Production Debugging

1. **Check logs**
   ```bash
   # Application logs
   tail -f logs/app.log
   
   # Database logs
   tail -f logs/db.log
   ```

2. **Performance monitoring**
   - Use Vercel Analytics (if deployed on Vercel)
   - Implement custom monitoring

## Troubleshooting

### Common Issues

1. **Database connection errors**
   - Check PostgreSQL is running
   - Verify DATABASE_URL in .env.local
   - Ensure database exists and user has permissions

2. **Build errors**
   - Clear Next.js cache: `rm -rf .next`
   - Reinstall dependencies: `rm -rf node_modules && pnpm install`
   - Check TypeScript errors: `pnpm type-check`

3. **Authentication issues**
   - Verify OAuth app configuration
   - Check NEXTAUTH_URL matches your domain
   - Ensure NEXTAUTH_SECRET is set

4. **Prisma issues**
   - Regenerate client: `pnpm db:generate`
   - Reset database: `pnpm db:reset` (destructive)
   - Check schema syntax

### Getting Help

- Check the [GitHub Issues](https://github.com/wenhaofree/aistak/issues)
- Review the [API Documentation](./API.md)
- Contact the maintainers

## Contributing

See [CONTRIBUTING.md](./CONTRIBUTING.md) for detailed contribution guidelines.
