generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  // 自增主键ID
  id             Int       @id @default(autoincrement())
  // 用户唯一标识UUID
  uuid           String    @unique
  // 用户邮箱
  email          String
  // 密码哈希，可选（用于邮箱密码登录）
  password       String?   @db.VarChar(255)
  // 创建时间，自动生成当前时间
  createdAt      DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 更新时间，自动更新
  updatedAt      DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted      Boolean   @default(false) @map("is_deleted")
  // 用户昵称，可选
  nickname       String?   @db.VarChar(255)
  // 用户头像URL，可选
  avatarUrl      String?   @map("avatar_url") @db.VarChar(255)
  // 用户区域/语言设置，可选
  locale         String?   @db.VarChar(50)
  // 登录类型，可选
  signinType     String?   @map("signin_type") @db.VarChar(50)
  // 登录IP地址，可选
  signinIp       String?   @map("signin_ip") @db.VarChar(255)
  // 登录提供商(如Google、Facebook等)，可选
  signinProvider String?   @map("signin_provider") @db.VarChar(50)
  // 第三方登录的OpenID，可选
  signinOpenid   String?   @map("signin_openid") @db.VarChar(255)
  // 用户订单关联
  orders         Order[]

  // 联合唯一索引：确保同一登录提供商下邮箱唯一
  @@unique([email, signinProvider])
  // 数据库表映射名称
  @@map("users")
}

model Order {
  // 自增主键ID
  id               Int       @id @default(autoincrement())
  // 订单编号，唯一
  orderNo          String    @unique @map("order_no") @db.VarChar(255)
  // 订单创建时间
  createdAt        DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  // 订单更新时间
  updatedAt        DateTime  @updatedAt @map("updated_at") @db.Timestamptz(6)
  // 是否已删除标记
  isDeleted        Boolean   @default(false) @map("is_deleted")
  // 用户UUID，关联User表
  userUuid         String    @map("user_uuid") @db.VarChar(255)
  // 用户邮箱
  userEmail        String    @map("user_email") @db.VarChar(255)
  // 订单金额
  amount           Int
  // 计费周期间隔（月、年等）
  interval         String?   @db.VarChar(50)
  // 订单过期时间
  expiredAt        DateTime? @map("expired_at") @db.Timestamptz(6)
  // 订单状态
  status           String    @db.VarChar(50)
  // Stripe支付会话ID
  stripeSessionId  String?   @map("stripe_session_id") @db.VarChar(255)
  // 订单包含的积分数量
  credits          Int
  // 货币类型
  currency         String?   @db.VarChar(50)
  // 订阅ID
  subId            String?   @map("sub_id") @db.VarChar(255)
  // 订阅间隔计数
  subIntervalCount Int?      @map("sub_interval_count")
  // 订阅周期锚点
  subCycleAnchor   Int?      @map("sub_cycle_anchor")
  // 订阅周期结束时间
  subPeriodEnd     Int?      @map("sub_period_end")
  // 订阅周期开始时间
  subPeriodStart   Int?      @map("sub_period_start")
  // 订阅次数
  subTimes         Int?      @map("sub_times")
  // 产品ID
  productId        String?   @map("product_id") @db.VarChar(255)
  // 产品名称
  productName      String?   @map("product_name") @db.VarChar(255)
  // 有效月数
  validMonths      Int?      @map("valid_months")
  // 订单详情（可存储JSON）
  orderDetail      String?   @map("order_detail")
  // 支付时间
  paidAt           DateTime? @map("paid_at") @db.Timestamptz(6)
  // 支付邮箱
  paidEmail        String?   @map("paid_email") @db.VarChar(255)
  // 支付详情
  paidDetail       String?   @map("paid_detail")
  // 关联用户
  user             User      @relation(fields: [userUuid], references: [uuid])

  // 数据库表映射名称
  @@map("orders")
}

/// 工具分类表 - 存储AI工具分类的基本信息
model Category {
  /// 自增主键ID
  id          Int              @id @default(autoincrement())
  /// 分类唯一标识符(英文短横线分隔) 如: ai-code-assistant
  slug        String           @unique @db.VarChar(50)
  /// 分类图标URL
  iconUrl     String?          @map("icon_url") @db.Text
  /// 分类级别 (1: 一级分类, 2: 二级分类)
  level       Int              @default(2) @map("level")
  /// 主分类标识符 (如:text-writing,image-generation等)
  mainCategory String?         @map("main_category") @db.VarChar(50)
  /// 父分类ID (如果是二级分类则关联到一级分类)
  parentId    Int?             @map("parent_id")
  /// 排序权重，值越大越靠前
  weight      Int              @default(0) @map("weight")
  /// 创建时间
  createdAt   DateTime         @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime         @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 分类的多语言翻译
  translations CategoryTranslation[]
  /// 关联到该分类的工具
  tools       ToolCategory[]
  /// 父分类关联 (自引用关系)
  parent      Category?        @relation("CategoryHierarchy", fields: [parentId], references: [id], onDelete: SetNull)
  /// 子分类列表
  children    Category[]       @relation("CategoryHierarchy")

  /// 数据库表名映射
  @@map("categories")
  /// 在slug字段上创建索引，加快查询
  @@index([slug])
  /// 在level字段上创建索引，加快按级别查询
  @@index([level])
  /// 在mainCategory字段上创建索引，加快按主分类查询
  @@index([mainCategory])
  /// 在parentId字段上创建索引，加快父子分类查询
  @@index([parentId])
}

/// 分类翻译表 - 存储分类的多语言翻译内容
model CategoryTranslation {
  /// 自增主键ID
  id          Int      @id @default(autoincrement())
  /// 关联的分类ID
  categoryId  Int      @map("category_id")
  /// 语言代码(如'en', 'zh')
  locale      String   @db.VarChar(10)
  /// 分类名称
  name        String   @db.VarChar(100)
  /// 分类描述
  description String?  @db.Text
  /// 创建时间
  createdAt   DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到分类表，当分类被删除时级联删除翻译
  category    Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  /// 确保同一分类在同一语言下只有一个翻译
  @@unique([categoryId, locale])
  /// 数据库表名映射
  @@map("category_translations")
  /// 在语言代码上创建索引，加快按语言查询
  @@index([locale])
  /// 组合索引，加快查询特定分类的特定语言翻译
  @@index([categoryId, locale])
}

/// 标签表 - 存储AI工具标签的基本信息
model Tag {
  /// 自增主键ID
  id          Int       @id @default(autoincrement())
  /// 标签唯一标识符(英文短横线分隔) 如: ai-tool
  slug        String    @unique @db.VarChar(50)
  /// 创建时间
  createdAt   DateTime  @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 标签的多语言翻译
  translations TagTranslation[]
  /// 使用该标签的工具
  tools       ToolTag[]

  /// 数据库表名映射
  @@map("tags")
  /// 在slug字段上创建索引，加快标签查询
  @@index([slug])
}

/// 标签翻译表 - 存储标签的多语言翻译内容
model TagTranslation {
  /// 自增主键ID
  id        Int      @id @default(autoincrement())
  /// 关联的标签ID
  tagId     Int      @map("tag_id")
  /// 语言代码(如'en', 'zh')
  locale    String   @db.VarChar(10)
  /// 标签名称
  name      String   @db.VarChar(50)
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到标签表，当标签被删除时级联删除翻译
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  /// 确保同一标签在同一语言下只有一个翻译
  @@unique([tagId, locale])
  /// 数据库表名映射
  @@map("tag_translations")
  /// 在语言代码上创建索引，加快按语言查询
  @@index([locale])
  /// 组合索引，加快查询特定标签的特定语言翻译
  @@index([tagId, locale])
}

/// 工具表 - 存储AI工具的基本信息
model Tool {
  /// 自增主键ID
  id          Int          @id @default(autoincrement())
  /// 工具唯一标识符(英文短横线分隔) 如: chatgpt
  toolId      String       @unique @map("tool_id") @db.VarChar(50)
  /// 工具图标URL
  iconUrl     String       @map("icon_url") @db.Text
  /// 工具官方网站URL
  url         String       @db.Text
  /// logo的图片
  websiteLogo String?      @map("website_logo") @db.VarChar(255)
  /// 域名注册时间
  domainRegistrationDate DateTime? @map("domain_registration_date") @db.Timestamp(0)
  /// 价格类型(free/freemium/paid/subscription)
  pricingType String       @map("pricing_type") @db.VarChar(20)
  /// 是否为付费工具
  isPremium   Boolean      @default(false) @map("is_premium")
  /// 是否为新上线工具
  isNew       Boolean      @default(false) @map("is_new")
  /// 是否为推荐工具
  isFeatured  Boolean      @default(false) @map("is_featured")
  /// 工具评分(1.0-5.0)
  rating      Decimal?     @db.Decimal(2,1)
  /// 是否提供API接口
  apiAvailable Boolean      @default(false) @map("api_available")
  /// 开发者/发布者名称
  publisher   String?      @db.VarChar(100)
  /// 发布者网站URL
  publisherUrl String?      @map("publisher_url") @db.Text
  /// 服务条款网址
  termsUrl    String?      @map("terms_url") @db.Text
  /// 隐私政策网址
  privacyUrl  String?      @map("privacy_url") @db.Text
  /// 创建时间
  createdAt   DateTime     @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt   DateTime     @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 工具的多语言翻译
  translations ToolTranslation[]
  /// 工具所属的分类
  categories  ToolCategory[]
  /// 工具相关标签
  tags        ToolTag[]
  /// 工具的功能特性
  features    ToolFeature[]

  /// 数据库表名映射
  @@map("tools")
  /// 在toolId字段上创建索引加快查询
  @@index([toolId])
  /// 在isPremium字段上创建索引，加快查询付费工具
  @@index([isPremium])
  /// 在isNew字段上创建索引，加快查询新工具
  @@index([isNew])
  /// 在isFeatured字段上创建索引，加快查询推荐工具
  @@index([isFeatured])
}

/// 工具翻译表 - 存储工具的多语言翻译内容
model ToolTranslation {
  /// 自增主键ID
  id                Int      @id @default(autoincrement())
  /// 关联的工具ID
  toolId            String   @map("tool_id") @db.VarChar(50)
  /// 语言代码(如'en', 'zh')
  locale            String   @db.VarChar(10)
  /// 工具名称
  name              String   @db.VarChar(100)
  /// 工具简短描述
  description       String   @db.Text
  /// 工具详细描述
  longDescription   String?  @map("long_description") @db.Text
  /// 使用说明和指南
  usageInstructions String?  @map("usage_instructions") @db.Text
  /// 价格详情
  pricingDetails    String?  @map("pricing_details") @db.Text
  /// 集成信息和API说明
  integrationInfo   String?  @map("integration_info") @db.Text
  /// 创建时间
  createdAt         DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除翻译
  tool              Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)

  /// 确保同一工具在同一语言下只有一个翻译
  @@unique([toolId, locale])
  /// 数据库表名映射
  @@map("tool_translations")
  /// 在语言代码上创建索引，加快按语言查询
  @@index([locale])
  /// 组合索引，加快查询特定工具的特定语言翻译
  @@index([toolId, locale])
}

/// 工具功能特性表 - 存储工具的具体功能特点
model ToolFeature {
  /// 自增主键ID
  id        Int      @id @default(autoincrement())
  /// 关联的工具ID
  toolId    String   @map("tool_id") @db.VarChar(50)
  /// 语言代码(如'en', 'zh')
  locale    String   @db.VarChar(10)
  /// 功能特性描述
  feature   String   @db.VarChar(100)
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 更新时间
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除功能
  tool      Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)

  /// 确保同一工具在同一语言下的同一功能只记录一次
  @@unique([toolId, locale, feature])
  /// 数据库表名映射
  @@map("tool_features")
  /// 组合索引，加快查询特定工具的特定语言功能
  @@index([toolId, locale])
}

/// 工具-分类关联表 - 存储工具与分类的多对多关系
model ToolCategory {
  /// 工具ID(复合主键之一)
  toolId     String   @map("tool_id") @db.VarChar(50)
  /// 分类ID(复合主键之一)
  categoryId Int      @map("category_id")
  /// 创建时间
  createdAt  DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除关联
  tool       Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)
  /// 关联到分类表，当分类被删除时级联删除关联
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  /// 设置复合主键，确保工具和分类的关联唯一
  @@id([toolId, categoryId])
  /// 数据库表名映射
  @@map("tool_categories")
  /// 在toolId字段上创建索引，加快查询特定工具的所有分类
  @@index([toolId])
  /// 在categoryId字段上创建索引，加快查询特定分类的所有工具
  @@index([categoryId])
}

/// 工具-标签关联表 - 存储工具与标签的多对多关系
model ToolTag {
  /// 工具ID(复合主键之一)
  toolId    String   @map("tool_id") @db.VarChar(50)
  /// 标签ID(复合主键之一)
  tagId     Int      @map("tag_id")
  /// 创建时间
  createdAt DateTime @default(now()) @map("created_at") @db.Timestamptz(6)
  /// 关联到工具表，当工具被删除时级联删除关联
  tool      Tool     @relation(fields: [toolId], references: [toolId], onDelete: Cascade)
  /// 关联到标签表，当标签被删除时级联删除关联
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  /// 设置复合主键，确保工具和标签的关联唯一
  @@id([toolId, tagId])
  /// 数据库表名映射
  @@map("tool_tags")
  /// 在toolId字段上创建索引，加快查询特定工具的所有标签
  @@index([toolId])
  /// 在tagId字段上创建索引，加快查询特定标签的所有工具
  @@index([tagId])
}
