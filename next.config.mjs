import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin();

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // 环境变量配置
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  },
  // 生产环境优化
  compress: process.env.NODE_ENV === 'production',
  poweredByHeader: false,
  generateEtags: true,

  // SEO优化
  trailingSlash: false,

  // 安全头部
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },
  // 缓存策略
  onDemandEntries: {
    maxInactiveAge: 60 * 1000,
    pagesBufferLength: 5,
  },
  // 构建优化
  // swcMinify: true, // 此选项在Next.js 15中已不再支持，默认启用
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production',
  },
  // 实验性功能
  experimental: {
    optimizeCss: {
      cssModules: true,
      // 添加critters配置
      critters: {
        preload: 'media',
        preloadFonts: true,
      }
    },
    optimizePackageImports: ['@radix-ui/react-icons', 'lucide-react'],
  },
  // 输出优化
  output: 'standalone',
  // 允许的开发源
  allowedDevOrigins: [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:3004',
    'http://127.0.0.1:3004',
    'https://a.aistak.com',
    'http://a.aistak.com',
    'https://www.aistak.com',
    'http://aistak.com',
  ],
  images: {
    unoptimized: false, // 默认即可
    // 支持的图片格式
    formats: ['image/avif', 'image/webp'],
    // 设备尺寸断点
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    // 图片尺寸断点
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // 最小缓存时间(7天)
    minimumCacheTTL: 60 * 60 * 24 * 7,
    // 允许的远程图片域名
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '**.aliyuncs.com',
      },
      {
        protocol: 'https',
        hostname: '**.oss-accelerate.aliyuncs.com',
      },
      {
        protocol: 'https',
        hostname: '**.picsum.photos',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
      },
      {
        protocol: 'https',
        hostname: '**.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'cdn-images.toolify.ai',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'img.aistak.com',
        pathname: '/**',
      },
    ],
    // 允许SVG
    dangerouslyAllowSVG: true,
    // SVG安全策略
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Removed the rewrites function as data fetching is now handled directly via Prisma
};

export default withNextIntl(nextConfig);
