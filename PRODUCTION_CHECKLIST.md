# 生产环境部署检查清单

## 🗄️ 数据库优化

### ✅ 已完成的优化
- [x] 移除开发环境调试日志 (`console.log`)
- [x] 优化Prisma日志配置（生产环境只保留错误日志）
- [x] 实施数据库连接池管理（生产环境最大2个连接）
- [x] 添加错误日志过滤（生产环境隐藏敏感信息）
- [x] 实施查询互斥锁机制
- [x] 优化缓存策略（生产环境30分钟TTL）

### 🔧 需要手动执行的数据库优化

#### 1. 添加推荐的数据库索引
```sql
-- 核心性能索引
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_updated_at_desc ON tools (updated_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tool_translations_locale_tool_id ON tool_translations (locale, tool_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tool_categories_category_id_tool_id ON tool_categories (category_id, tool_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_slug_level ON categories (slug, level);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_category_translations_locale_category_id ON category_translations (locale, category_id);

-- 复合索引（提升复杂查询性能）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_featured_premium_updated ON tools (is_featured, is_premium, updated_at DESC);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_new_updated ON tools (is_new, updated_at DESC) WHERE is_new = true;

-- 部分索引（节省空间，提升特定查询）
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tools_active ON tools (updated_at DESC) WHERE is_deleted = false;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_active ON categories (slug) WHERE level IN (1, 2);
```

#### 2. 数据库连接池配置
```bash
# 生产环境数据库URL示例
DATABASE_URL='************************************/database?connection_limit=5&pool_timeout=30&connect_timeout=10&sslmode=require'
```

## 🚀 性能优化

### SQL查询优化建议

#### 当前查询问题：
1. **工具列表查询**：分离的 `findMany` 和 `count` 查询
2. **分类查询**：多次 `$queryRaw` 调用
3. **串行执行**：虽然避免了连接问题，但影响性能

#### 优化方案：
1. **使用批量查询**：`SQLOptimizer.getBatchCategoryTools()`
2. **合并查询**：使用CTE和窗口函数
3. **缓存策略**：增加缓存时间到30分钟

### 🔍 监控和日志

#### 生产环境日志配置：
- ✅ Prisma：只记录错误日志
- ✅ 应用：移除调试日志
- ✅ 错误处理：隐藏敏感信息

#### 性能监控：
```typescript
// 使用性能监控工具
import { analyzeQueryPerformance } from '@/lib/sql-optimizer';

// 定期分析查询性能
setInterval(analyzeQueryPerformance, 300000); // 每5分钟
```

## 🛡️ 安全配置

### 数据库安全：
- [ ] 启用SSL连接 (`sslmode=require`)
- [ ] 限制数据库用户权限
- [ ] 定期备份数据库
- [ ] 监控异常连接

### 应用安全：
- [ ] 设置适当的CORS策略
- [ ] 启用请求限制
- [ ] 配置安全头部
- [ ] 环境变量加密

## 📊 缓存策略

### 当前缓存配置：
- **开发环境**：10分钟TTL
- **生产环境**：30分钟TTL
- **最大缓存项**：1000个

### 缓存键策略：
```typescript
// 缓存键格式
`tools_latest_${locale}_${Math.floor(Date.now() / (1000 * 60 * 30))}` // 30分钟
`tools_category_${categorySlug}_${locale}_${Math.floor(Date.now() / (1000 * 60 * 30))}`
```

## 🔧 环境变量配置

### 生产环境必需变量：
```bash
NODE_ENV=production
DATABASE_URL=postgresql://...?connection_limit=5&pool_timeout=30&sslmode=require
NEXTAUTH_URL=https://yourdomain.com
AUTH_SECRET=your-secret-key
```

### 可选优化变量：
```bash
# 数据库连接优化
DB_MAX_CONNECTIONS=5
DB_QUERY_TIMEOUT=60000
DB_CONNECTION_TIMEOUT=30000

# 缓存配置
CACHE_TTL=1800
CACHE_MAX_SIZE=1000

# 监控配置
ENABLE_PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=5000
```

## 📈 性能基准

### 目标性能指标：
- **首页加载时间**：< 3秒
- **数据库查询时间**：< 1秒
- **缓存命中率**：> 80%
- **并发连接数**：< 5个

### 监控命令：
```bash
# 检查数据库连接
curl http://localhost:3000/api/health/db

# 分析查询性能
npm run analyze-performance
```

## 🚨 故障排除

### 常见问题：
1. **连接池耗尽**：检查 `maxConnections` 配置
2. **查询超时**：增加 `queryTimeout` 设置
3. **缓存失效**：检查缓存键生成逻辑
4. **内存泄漏**：监控缓存大小和清理机制

### 紧急处理：
```bash
# 重启应用
pm2 restart app

# 清理缓存
curl -X POST http://localhost:3000/api/cache/clear

# 检查数据库状态
psql $DATABASE_URL -c "SELECT count(*) FROM pg_stat_activity;"
```

## ✅ 部署前检查

- [ ] 运行所有测试
- [ ] 检查环境变量配置
- [ ] 验证数据库连接
- [ ] 测试缓存功能
- [ ] 确认日志配置
- [ ] 验证性能指标
- [ ] 检查安全配置
- [ ] 准备监控告警

## 📝 部署后验证

- [ ] 检查应用启动日志
- [ ] 验证数据库连接状态
- [ ] 测试关键页面加载
- [ ] 确认缓存工作正常
- [ ] 监控性能指标
- [ ] 检查错误日志
- [ ] 验证安全配置生效
