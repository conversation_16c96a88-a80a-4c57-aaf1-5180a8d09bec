# Contributing to AISTAK

Thank you for your interest in contributing to AISTAK! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Process](#development-process)
- [Coding Standards](#coding-standards)
- [Submitting Changes](#submitting-changes)
- [Issue Guidelines](#issue-guidelines)
- [Pull Request Process](#pull-request-process)
- [Community](#community)

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct:

- **Be respectful** and inclusive in all interactions
- **Be constructive** when providing feedback
- **Be patient** with new contributors
- **Focus on the issue**, not the person
- **Help create a welcoming environment** for everyone

## Getting Started

### Prerequisites

Before contributing, ensure you have:

- Node.js 18+ installed
- pnpm package manager
- PostgreSQL database
- Git configured with your GitHub account
- Basic knowledge of Next.js, TypeScript, and React

### Development Setup

1. **Fork the repository**
   ```bash
   # Fork on GitHub, then clone your fork
   git clone https://github.com/YOUR_USERNAME/aistak.git
   cd aistak
   ```

2. **Set up development environment**
   ```bash
   # Install dependencies
   pnpm install
   
   # Copy environment template
   cp .env.example .env.local
   
   # Configure your environment variables
   # See docs/DEVELOPMENT.md for detailed setup
   ```

3. **Start development server**
   ```bash
   pnpm dev
   ```

4. **Verify setup**
   - Visit http://localhost:3000
   - Ensure the application loads correctly
   - Run tests: `pnpm test`

## Development Process

### Branching Strategy

We use a feature branch workflow:

```bash
# Create feature branch from main
git checkout main
git pull origin main
git checkout -b feature/your-feature-name

# Make your changes
# ...

# Push to your fork
git push origin feature/your-feature-name
```

### Branch Naming Convention

- `feature/` - New features
- `fix/` - Bug fixes
- `docs/` - Documentation updates
- `refactor/` - Code refactoring
- `test/` - Adding or updating tests
- `chore/` - Maintenance tasks

Examples:
- `feature/add-tool-search`
- `fix/authentication-redirect`
- `docs/update-api-documentation`

## Coding Standards

### TypeScript

- Use TypeScript for all new code
- Define proper types and interfaces
- Avoid `any` type unless absolutely necessary
- Use strict mode settings

```typescript
// Good
interface ToolProps {
  id: string;
  name: string;
  description: string;
  category: Category;
}

// Avoid
const tool: any = { ... };
```

### React Components

- Use functional components with hooks
- Implement proper prop types
- Use meaningful component names
- Keep components focused and reusable

```typescript
// Good
interface ToolCardProps {
  tool: Tool;
  locale: string;
  onSelect?: (tool: Tool) => void;
}

export function ToolCard({ tool, locale, onSelect }: ToolCardProps) {
  // Component implementation
}
```

### Code Style

We use ESLint and Prettier for consistent code formatting:

```bash
# Check code style
pnpm lint

# Fix auto-fixable issues
pnpm lint:fix

# Format code
pnpm format
```

### File Organization

```
src/
├── app/                    # Next.js App Router
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── sections/         # Page sections
│   └── blocks/           # Content blocks
├── lib/                  # Utility functions
├── types/                # Type definitions
├── constants/            # Application constants
└── hooks/                # Custom React hooks
```

### Naming Conventions

- **Files**: Use kebab-case for files (`tool-card.tsx`)
- **Components**: Use PascalCase (`ToolCard`)
- **Functions**: Use camelCase (`getUserTools`)
- **Constants**: Use UPPER_SNAKE_CASE (`API_BASE_URL`)
- **Types/Interfaces**: Use PascalCase (`ToolCategory`)

## Submitting Changes

### Commit Messages

We follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

**Examples:**
```
feat(tools): add advanced search functionality

- Implement search by category and tags
- Add sorting options
- Include pagination for results

Closes #123
```

```
fix(auth): resolve OAuth redirect issue

The OAuth callback was not handling the state parameter correctly,
causing authentication failures in production.

Fixes #456
```

### Testing

Before submitting changes:

1. **Run all tests**
   ```bash
   pnpm test
   ```

2. **Test your changes manually**
   - Verify functionality works as expected
   - Test edge cases
   - Check responsive design

3. **Add tests for new features**
   ```typescript
   // Example test
   describe('ToolCard', () => {
     it('should render tool information correctly', () => {
       // Test implementation
     });
   });
   ```

## Issue Guidelines

### Reporting Bugs

When reporting bugs, include:

1. **Clear description** of the issue
2. **Steps to reproduce** the problem
3. **Expected behavior** vs actual behavior
4. **Environment details** (browser, OS, etc.)
5. **Screenshots** if applicable
6. **Error messages** or console logs

**Bug Report Template:**
```markdown
## Bug Description
Brief description of the bug

## Steps to Reproduce
1. Go to '...'
2. Click on '...'
3. See error

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Environment
- Browser: Chrome 91
- OS: macOS 12.0
- Node.js: 18.17.0

## Additional Context
Any other relevant information
```

### Feature Requests

For feature requests, include:

1. **Problem description** - What problem does this solve?
2. **Proposed solution** - How should it work?
3. **Alternatives considered** - Other approaches you've thought about
4. **Use cases** - When would this be useful?

## Pull Request Process

### Before Submitting

1. **Ensure your branch is up to date**
   ```bash
   git checkout main
   git pull origin main
   git checkout your-feature-branch
   git rebase main
   ```

2. **Run quality checks**
   ```bash
   pnpm lint
   pnpm test
   pnpm type-check
   ```

3. **Update documentation** if needed

### Pull Request Template

```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

## Screenshots
If applicable, add screenshots

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes (or clearly documented)
```

### Review Process

1. **Automated checks** must pass
2. **Code review** by maintainers
3. **Testing** in staging environment
4. **Approval** from project maintainers
5. **Merge** to main branch

### Review Criteria

Reviewers will check for:

- **Code quality** and adherence to standards
- **Functionality** works as intended
- **Performance** impact
- **Security** considerations
- **Documentation** completeness
- **Test coverage**

## Community

### Getting Help

- **GitHub Discussions** - For questions and general discussion
- **GitHub Issues** - For bug reports and feature requests
- **Email** - Contact maintainers directly for sensitive issues

### Recognition

Contributors are recognized in:

- **README.md** - Contributors section
- **Release notes** - Feature acknowledgments
- **GitHub** - Contributor graphs and statistics

### Becoming a Maintainer

Regular contributors may be invited to become maintainers based on:

- **Consistent quality contributions**
- **Understanding of project goals**
- **Positive community interaction**
- **Technical expertise**

## Development Resources

### Useful Links

- [Next.js Documentation](https://nextjs.org/docs)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)

### Project Documentation

- [Development Guide](docs/DEVELOPMENT.md)
- [API Documentation](docs/API.md)
- [Deployment Guide](docs/DEPLOYMENT.md)

## Questions?

If you have questions about contributing, please:

1. Check existing documentation
2. Search GitHub issues and discussions
3. Create a new discussion or issue
4. Contact maintainers directly

Thank you for contributing to AISTAK! 🚀
