import { NextResponse } from 'next/server';
import { checkDatabaseHealth } from '@/lib/db-utils';
import { cache } from '@/lib/cache';

export async function GET() {
  try {
    // 检查数据库健康状态
    const dbHealth = await checkDatabaseHealth();
    
    // 获取缓存统计信息
    const cacheStats = cache.getStats();
    
    // 检查数据库连接
    const response = {
      status: dbHealth.isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: {
        isHealthy: dbHealth.isHealthy,
        connectionStatus: dbHealth.connectionStatus,
        error: dbHealth.error || null
      },
      cache: {
        size: cacheStats.size,
        keys: cacheStats.keys.length,
        sampleKeys: cacheStats.keys.slice(0, 5) // 只显示前5个键作为示例
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        databaseUrl: process.env.DATABASE_URL ? 'configured' : 'missing'
      }
    };

    return NextResponse.json(response, {
      status: dbHealth.isHealthy ? 200 : 503
    });
  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, {
      status: 500
    });
  }
}
