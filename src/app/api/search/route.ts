import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  // 获取搜索参数
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('q');
  const locale = searchParams.get('locale') || 'zh';
  
  // 如果没有查询参数，返回空结果
  if (!query) {
    return NextResponse.json({ tools: [] });
  }
  
  try {
    // 从数据库中搜索工具
    // @ts-ignore
    const tools = await prisma.tool.findMany({
      where: {
        // 搜索翻译中包含查询词的工具
        translations: {
          some: {
            locale,
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { description: { contains: query, mode: 'insensitive' } }
            ]
          }
        }
      },
      take: 8, // 限制结果数量
      select: {
        id: true,
        toolId: true,
        iconUrl: true,
        url: true,
        pricingType: true,
        isPremium: true,
        isNew: true,
        isFeatured: true,
        rating: true,
        translations: {
          where: {
            locale,
          },
          select: {
            id: true,
            locale: true,
            name: true,
            description: true,
          }
        },
      },
    });
    
    // 转换Decimal类型为普通JavaScript数字类型
    const serializedTools = tools.map(tool => ({
      ...tool,
      // 如果rating存在，将其转换为number类型
      rating: tool.rating ? parseFloat(tool.rating.toString()) : null,
    }));
    
    return NextResponse.json({ tools: serializedTools });
  } catch (error) {
    console.error('搜索工具出错:', error);
    return NextResponse.json(
      { error: '搜索失败，请稍后再试' },
      { status: 500 }
    );
  }
} 