import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 设置缓存控制，将结果缓存10分钟
export const revalidate = 600; // 10分钟重新验证一次数据

export async function GET(request: NextRequest) {
  try {
    // 获取查询参数
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const locale = searchParams.get('locale') || 'en';
    const category = searchParams.get('category') || null;
    const sort = searchParams.get('sort') || null;
    const search = searchParams.get('search') || null;
    
    // 计算跳过的数量
    const skip = (page - 1) * limit;
    
    // 构建查询条件
    let where: any = {};
    let orderBy: any = { updatedAt: 'desc' }; // 默认排序

    // 如果有搜索参数，添加搜索条件
    if (search) {
      where.translations = {
        some: {
          locale,
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } }
          ]
        }
      };
    }
    
    // 根据排序参数设置排序方式
    switch (sort) {
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'updated':
        orderBy = { updatedAt: 'desc' };
        break;
      case 'rating':
        orderBy = { rating: 'desc' };
        break;
      case 'name':
        orderBy = { translations: { _count: 'desc' } }; // 临时方案，实际需要按名称排序
        break;
      case 'newest-registered':
        orderBy = {
          domainRegistrationDate: 'desc'
        };
        // 确保只显示有注册日期的工具
        where.domainRegistrationDate = {
          not: null
        };
        break;
      case 'popular':
      case 'views':
      case 'favorites':
        // 这些排序需要额外的字段支持，暂时使用更新时间排序
        orderBy = { updatedAt: 'desc' };
        break;
      default:
        orderBy = { updatedAt: 'desc' };
        break;
    }
    
    // 如果有分类参数，添加分类过滤
    if (category) {
      // 先查询该分类的信息，以确定是否为一级分类
      const [categoryInfo] = await prisma.$queryRaw<any[]>`
        SELECT id, level FROM categories WHERE slug = ${category}
      `;
      
      if (!categoryInfo) {
        // 分类不存在，返回空结果
        return NextResponse.json({
          tools: [],
          pagination: {
            total: 0,
            page,
            limit,
            totalPages: 0,
          },
        });
      }
      
      let categoryIds = [categoryInfo.id];
      
      // 如果是一级分类，获取所有子分类ID
      if (categoryInfo.level === 1) {
        const subcategories = await prisma.$queryRaw<any[]>`
          SELECT id FROM categories WHERE parent_id = ${categoryInfo.id}
        `;
        categoryIds = categoryIds.concat(subcategories.map(sub => sub.id));
      }
      
      // 构建查询条件，查询包含指定分类ID的工具
      where.categories = {
        some: {
          categoryId: {
            in: categoryIds
          }
        }
      };
    }
    
    // 查询工具，按更新时间降序排序
    // @ts-ignore
    const tools = await prisma.tool.findMany({
      where,
      skip,
      take: limit,
      orderBy,
      select: {
        id: true,
        toolId: true,
        iconUrl: true,
        url: true,
        pricingType: true,
        isPremium: true,
        isNew: true,
        isFeatured: true,
        rating: true,
        apiAvailable: true,
        createdAt: true,
        translations: {
          where: {
            locale,
          },
          select: {
            id: true,
            locale: true,
            name: true,
            description: true,
          }
        },
        categories: {
          select: {
            categoryId: true,
            category: {
              select: {
                id: true,
                slug: true,
                translations: {
                  where: {
                    locale,
                  },
                  select: {
                    id: true,
                    locale: true,
                    name: true,
                  }
                },
              },
            },
          },
        }
      },
    });
    
    // 获取总数（应用相同的过滤条件）
    // @ts-ignore
    const total = await prisma.tool.count({
      where
    });
    
    // 转换Decimal类型为普通JavaScript数字
    const serializedTools = tools.map(tool => ({
      ...tool,
      // 如果rating存在，将其转换为number类型
      rating: tool.rating ? parseFloat(tool.rating.toString()) : null,
    }));
    
    // 添加缓存控制头
    const response = NextResponse.json({
      tools: serializedTools,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    });
    
    // 设置缓存控制头
    response.headers.set('Cache-Control', 'public, s-maxage=600, stale-while-revalidate=86400');
    
    return response;
  } catch (error) {
    console.error('Error fetching tools:', error);
    return NextResponse.json({ error: '获取工具列表失败' }, { status: 500 });
  }
} 