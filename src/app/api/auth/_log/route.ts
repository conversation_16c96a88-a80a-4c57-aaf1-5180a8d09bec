import { NextResponse } from 'next/server';

/**
 * Authentication logging endpoint
 * This endpoint is used by NextAuth.js for internal logging
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json();
    
    // Log authentication events for debugging
    console.log('Auth log:', {
      timestamp: new Date().toISOString(),
      ...body
    });
    
    // Return success response
    return NextResponse.json({ 
      success: true,
      message: 'Log recorded'
    });
  } catch (error) {
    console.error('Auth log error:', error);
    
    // Return error response
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to record log' 
      },
      { status: 500 }
    );
  }
}

/**
 * Handle GET requests (optional)
 */
export async function GET() {
  return NextResponse.json({ 
    message: 'Auth logging endpoint is active',
    timestamp: new Date().toISOString()
  });
}
