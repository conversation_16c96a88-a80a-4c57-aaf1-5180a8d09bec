import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 设置缓存控制，将结果缓存10分钟
export const revalidate = 600; // 10分钟重新验证一次数据

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    const searchParams = request.nextUrl.searchParams;
    const locale = searchParams.get('locale') || 'en';
    
    if (!slug) {
      return NextResponse.json({ error: '缺少分类标识符' }, { status: 400 });
    }
    
    // 查询分类信息
    // @ts-ignore
    const category = await prisma.category.findUnique({
      where: {
        slug: slug,
      },
      select: {
        id: true,
        slug: true,
        iconUrl: true,
        translations: {
          where: {
            locale,
          },
          select: {
            id: true,
            locale: true,
            name: true,
            description: true,
          }
        },
        // 计算关联的工具数量
        _count: {
          select: {
            tools: true
          }
        }
      },
    });
    
    if (!category) {
      return NextResponse.json({ error: '分类不存在' }, { status: 404 });
    }
    
    // 添加缓存控制头
    const response = NextResponse.json({
      category,
    });
    
    // 设置缓存控制头
    response.headers.set('Cache-Control', 'public, s-maxage=600, stale-while-revalidate=86400');
    
    return response;
  } catch (error) {
    console.error('Error fetching category:', error);
    return NextResponse.json({ error: '获取分类信息失败' }, { status: 500 });
  }
} 