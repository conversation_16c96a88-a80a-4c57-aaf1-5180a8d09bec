import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 设置缓存控制，将结果缓存10分钟
export const revalidate = 600; // 10分钟重新验证一次数据

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const locale = searchParams.get('locale') || 'en';
    const level = searchParams.get('level');
    
    // 构建查询条件
    let where: any = {};
    if (level) {
      where.level = parseInt(level);
    }
    
    // 查询分类
    // @ts-ignore
    const categories = await prisma.category.findMany({
      where,
      select: {
        id: true,
        slug: true,
        iconUrl: true,
        level: true,
        weight: true,
        parentId: true,
        translations: {
          where: {
            locale,
          },
          select: {
            id: true,
            locale: true,
            name: true,
            description: true,
          }
        },
        // 计算关联的工具数量
        _count: {
          select: {
            tools: true
          }
        }
      },
      orderBy: {
        weight: 'desc'
      }
    });
    
    // 添加缓存控制头
    const response = NextResponse.json({
      categories,
    });
    
    // 设置缓存控制头
    response.headers.set('Cache-Control', 'public, s-maxage=600, stale-while-revalidate=86400');
    
    return response;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return NextResponse.json({ error: '获取分类列表失败' }, { status: 500 });
  }
}
