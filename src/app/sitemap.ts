import { MetadataRoute } from 'next';
import { prisma } from '@/lib/prisma';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aistak.com';
  const locales = ['en', 'zh'];
  
  const sitemap: MetadataRoute.Sitemap = [];

  // Static pages
  const staticPages = [
    '',
    '/tools',
    '/categories',
    '/about',
    '/contact',
    '/privacy',
    '/terms',
  ];

  // Add static pages for each locale
  for (const locale of locales) {
    for (const page of staticPages) {
      sitemap.push({
        url: `${baseUrl}/${locale}${page}`,
        lastModified: new Date(),
        changeFrequency: page === '' ? 'daily' : 'weekly',
        priority: page === '' ? 1.0 : 0.8,
        alternates: {
          languages: locales.reduce((acc, loc) => {
            acc[loc] = `${baseUrl}/${loc}${page}`;
            return acc;
          }, {} as Record<string, string>),
        },
      });
    }
  }

  try {
    // Get all tools
    const tools = await prisma.tool.findMany({
      select: {
        toolId: true,
        updatedAt: true,
        translations: {
          select: {
            locale: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    // Add tool pages
    for (const tool of tools) {
      for (const locale of locales) {
        // Check if translation exists for this locale
        const hasTranslation = tool.translations.some(t => t.locale === locale);
        if (hasTranslation) {
          sitemap.push({
            url: `${baseUrl}/${locale}/tools/${tool.toolId}`,
            lastModified: tool.updatedAt,
            changeFrequency: 'weekly',
            priority: 0.7,
            alternates: {
              languages: locales.reduce((acc, loc) => {
                const localeHasTranslation = tool.translations.some(t => t.locale === loc);
                if (localeHasTranslation) {
                  acc[loc] = `${baseUrl}/${loc}/tools/${tool.toolId}`;
                }
                return acc;
              }, {} as Record<string, string>),
            },
          });
        }
      }
    }

    // Get all categories
    const categories = await prisma.category.findMany({
      select: {
        slug: true,
        updatedAt: true,
        translations: {
          select: {
            locale: true,
          },
        },
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    // Add category pages
    for (const category of categories) {
      for (const locale of locales) {
        // Check if translation exists for this locale
        const hasTranslation = category.translations.some(t => t.locale === locale);
        if (hasTranslation) {
          sitemap.push({
            url: `${baseUrl}/${locale}/categories/${category.slug}`,
            lastModified: category.updatedAt,
            changeFrequency: 'weekly',
            priority: 0.6,
            alternates: {
              languages: locales.reduce((acc, loc) => {
                const localeHasTranslation = category.translations.some(t => t.locale === loc);
                if (localeHasTranslation) {
                  acc[loc] = `${baseUrl}/${loc}/categories/${category.slug}`;
                }
                return acc;
              }, {} as Record<string, string>),
            },
          });
        }
      }
    }

  } catch (error) {
    console.error('Error generating sitemap:', error);
  }

  return sitemap;
}
