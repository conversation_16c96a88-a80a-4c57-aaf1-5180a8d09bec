import {NextIntlClientProvider} from 'next-intl';
import {getMessages} from '@/i18n/routing';
import {notFound} from 'next/navigation';
import {routing} from '@/i18n/routing';
import '../globals.css';
import Header from '@/components/Header';
// import Footer from '@/components/Footer';
import { getLandingPage } from '@/app/actions';
import { Providers } from '@/app/providers';
// import { SplashCursor } from "@/components/ui/splash-cursor"
import { Footer } from '@/components/ui/footer-section';
import Script from 'next/script';
import type { Metadata } from 'next';
import { generateSEOMetadata, generateOrganizationSchema, generateWebsiteSchema, SEO_CONFIG } from '@/lib/seo';

// 定义全局元数据 (使用增强的SEO配置)
export const metadata: Metadata = {
  ...generateSEOMetadata({
    title: SEO_CONFIG.defaultTitle,
    description: SEO_CONFIG.defaultDescription,
    keywords: SEO_CONFIG.defaultKeywords,
    url: SEO_CONFIG.siteUrl,
    type: 'website'
  }),
  metadataBase: new URL(SEO_CONFIG.siteUrl)
};

// 修改类型定义，使用 generateStaticParams 来处理参数
export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

// 更新 Layout 组件以支持异步 params
export default async function Layout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;
  
  // 验证 locale
  if (!routing.locales.includes(locale as any)) {
    notFound();
  }
 
  // 并行获取消息和页面数据
  const [messages, page] = await Promise.all([
    getMessages(locale),
    getLandingPage(locale)
  ]);

  return (
    <html lang={locale} className="scroll-smooth" suppressHydrationWarning>
      <head>
        {/* Structured Data */}
        <Script
          id="organization-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: generateOrganizationSchema(),
          }}
        />
        <Script
          id="website-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: generateWebsiteSchema(),
          }}
        />

        {/* Google tag (gtag.js) */}
        <Script
          strategy="afterInteractive"
          src="https://www.googletagmanager.com/gtag/js?id=G-9ST4TEHF2S"
        />
        <Script
          id="google-analytics-init"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-9ST4TEHF2S');
            `,
          }}
        />
      </head>
      <body>
          <Script
            src="https://umami.wenhaofree.com/script.js"
            data-website-id="8beda2bf-5112-490a-b6ba-22dfdc75a47f"
            defer
          />
          {/* Mouse smoke effect */}
          {/* <SplashCursor /> */}
          <NextIntlClientProvider messages={messages} locale={locale}>
          <Providers>
            {/* Header is now fixed positioned within the component itself */}
            {page.header && <Header header={page.header} />}

            {/* Main content with dynamic top padding to account for header height */}
            <main className="flex-1" style={{ paddingTop: 'calc(4rem + var(--header-banner-height, 0px))' }}>
                <div className="lg:container mx-auto px-5 lg:px-4 xl:px-1">
                  <div className="flex flex-col gap-4 pt-4">
                    {children}
                  </div>
                </div>
            </main>
            {/* <div className="border-t">
              {page.footer && <Footer footer={page.footer} />}
            </div> */}
            {page.footer && <Footer footer={page.footer} />}
            </Providers>
          </NextIntlClientProvider>
        
      </body>
    </html>
  );
}
