import { useTranslations } from 'next-intl';
import { generateSEOMetadata } from '@/lib/seo';
import { routing } from '@/i18n/routing';
import type { Metadata } from 'next';

interface PrivacyPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

export async function generateMetadata({ params }: PrivacyPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  return generateSEOMetadata({
    title: locale === 'zh' ? '隐私政策 - AISTAK' : 'Privacy Policy - AISTAK',
    description: locale === 'zh'
      ? 'AISTAK 隐私政策 - 了解我们如何收集、使用和保护您的个人信息。我们承诺不出售您的个人信息。'
      : 'AISTAK Privacy Policy - Learn how we collect, use, and protect your personal information. We are committed to not selling your personal information.',
    url: `/${locale}/privacy`,
    locale,
  });
}

export default function PrivacyPage() {
  const t = useTranslations('privacy');

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-4xl">
        <div className="prose prose-gray dark:prose-invert max-w-none">
          <h1 className="text-4xl font-bold text-foreground mb-8">
            {t('title')}
          </h1>
          
          <div className="text-muted-foreground mb-8">
            <p className="text-lg leading-relaxed">
              {t('welcome')}
            </p>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 mb-8">
            <p className="text-blue-800 dark:text-blue-200 font-semibold">
              {t('important_notice')}
            </p>
          </div>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section1.title')}
            </h2>
            
            <h3 className="text-xl font-medium text-foreground mb-3">
              {t('section1.usage_data.title')}
            </h3>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section1.usage_data.content')}
            </p>

            <h3 className="text-xl font-medium text-foreground mb-3">
              {t('section1.device_info.title')}
            </h3>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section1.device_info.content')}
            </p>

            <h3 className="text-xl font-medium text-foreground mb-3">
              {t('section1.cookies.title')}
            </h3>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section1.cookies.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section2.title')}
            </h2>
            
            <h3 className="text-xl font-medium text-foreground mb-3">
              {t('section2.google_analytics.title')}
            </h3>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section2.google_analytics.content')}
            </p>

            <h3 className="text-xl font-medium text-foreground mb-3">
              {t('section2.google_signin.title')}
            </h3>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section2.google_signin.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section3.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section3.intro')}
            </p>
            <ul className="list-disc list-inside text-muted-foreground space-y-2 ml-4">
              <li>{t('section3.purposes.provide')}</li>
              <li>{t('section3.purposes.notify')}</li>
              <li>{t('section3.purposes.participate')}</li>
              <li>{t('section3.purposes.support')}</li>
              <li>{t('section3.purposes.analysis')}</li>
              <li>{t('section3.purposes.monitor')}</li>
              <li>{t('section3.purposes.detect')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section4.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section4.intro')}
            </p>
            <ul className="list-disc list-inside text-muted-foreground space-y-2 ml-4">
              <li>{t('section4.circumstances.consent')}</li>
              <li>{t('section4.circumstances.legal')}</li>
              <li>{t('section4.circumstances.protect')}</li>
              <li>{t('section4.circumstances.prevent')}</li>
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section5.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section5.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section6.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section6.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('contact.title')}
            </h2>
            <p className="text-muted-foreground leading-relaxed">
              {t('contact.content')}{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </section>
        </div>
      </div>
    </div>
  );
}
