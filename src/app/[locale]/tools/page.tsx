'use client';

import { useState, useEffect, useRef, Suspense } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { ToolCard } from '@/components/ToolCard';
import { Button } from '@/components/ui/button';
import { Tool } from '@/app/actions';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { ChevronRight } from 'lucide-react';
import ToolsFilter from '@/components/ToolsFilter';
import CategoryNavigation from '@/components/CategoryNavigation';





// 工具卡片骨架屏组件
function ToolCardSkeleton() {
  return (
    <div className="h-full rounded-lg border bg-card text-card-foreground shadow-sm animate-pulse">
      <div className="p-6 pb-2">
        <div className="flex justify-between items-start">
          <div className="w-12 h-12 bg-gray-200 rounded-md"></div>
          <div className="flex space-x-1">
            <div className="w-8 h-5 bg-gray-200 rounded-full"></div>
          </div>
        </div>
        <div className="h-6 bg-gray-200 rounded mt-4 w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded mt-2 w-1/3"></div>
      </div>
      <div className="p-6 pt-0">
        <div className="h-4 bg-gray-200 rounded mt-2 w-full"></div>
        <div className="h-4 bg-gray-200 rounded mt-2 w-full"></div>
        <div className="h-4 bg-gray-200 rounded mt-2 w-2/3"></div>
      </div>
      <div className="p-6 pt-0">
        <div className="flex justify-between items-center">
          <div className="h-4 bg-gray-200 rounded w-1/4"></div>
          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
        </div>
      </div>
    </div>
  );
}

// 工具卡片网格组件
function ToolsGrid({ tools, locale }: { tools: Tool[], locale: string }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {tools.map((tool) => (
        <ToolCard key={tool.toolId} tool={tool} locale={locale} />
      ))}
    </div>
  );
}

// 骨架屏网格
function SkeletonGrid() {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
      {[...Array(8)].map((_, index) => (
        <ToolCardSkeleton key={index} />
      ))}
    </div>
  );
}

// 加载指示器组件
function LoadingIndicator() {
  return (
    <div className="w-full flex justify-center py-6">
      <div className="flex items-center space-x-2">
        <div className="w-3 h-3 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-3 h-3 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-3 h-3 rounded-full bg-gray-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
    </div>
  );
}



export default function ToolsListPage() {
  const { locale } = useParams<{ locale: string }>();
  const searchParams = useSearchParams();
  const categorySlug = searchParams.get('category');
  const searchQuery = searchParams.get('search');
  const sortParam = searchParams.get('sort');
  
  const t = useTranslations('tools');
  const categoryT = useTranslations('categories');
  
  const [tools, setTools] = useState<Tool[]>([]);
  const [categoryName, setCategoryName] = useState<string>("");
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  
  // 创建一个引用来保存观察器目标元素
  const observerTarget = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 重置状态
    setTools([]);
    setPagination({
      total: 0,
      page: 1,
      limit: 20,
      totalPages: 0,
    });
    setLoading(true);
    setHasMore(true);

    // 获取分类名称
    if (categorySlug) {
      fetchCategoryName();
    }

    // 加载工具
    loadTools();
  }, [categorySlug, searchQuery, sortParam, locale]);

  // 获取分类名称
  const fetchCategoryName = async () => {
    if (!categorySlug) return;
    
    try {
      const response = await fetch(`/api/categories/${categorySlug}?locale=${locale || 'en'}`);
      if (response.ok) {
        const data = await response.json();
        if (data.category && data.category.translations.length > 0) {
          setCategoryName(data.category.translations[0].name);
        }
      }
    } catch (error) {
      console.error('Error fetching category name:', error);
    }
  };

  // 设置Intersection Observer来检测滚动
  useEffect(() => {
    // 如果正在加载或者没有更多数据，不创建观察器
    if (loading || !hasMore) return;
    
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && !loadingMore && pagination.page < pagination.totalPages) {
          loadTools(pagination.page + 1);
        }
      },
      { threshold: 0.1 } // 当目标元素10%可见时触发
    );
    
    const currentTarget = observerTarget.current;
    if (currentTarget) {
      observer.observe(currentTarget);
    }
    
    // 清理函数
    return () => {
      if (currentTarget) {
        observer.unobserve(currentTarget);
      }
    };
  }, [loading, loadingMore, pagination.page, pagination.totalPages, hasMore]);

  const loadTools = async (page = 1) => {
    try {
      if (page === 1) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }
      
      // 获取排序参数
      const sort = searchParams.get('sort');

      // 构建API URL
      const params = new URLSearchParams({
        locale: locale || 'en',
        page: page.toString(),
        limit: '20'
      });

      if (sort) params.set('sort', sort);
      if (categorySlug) params.set('category', categorySlug);
      if (searchQuery) params.set('search', searchQuery);
      if (sortParam && !sort) params.set('sort', sortParam);

      const apiUrl = `/api/tools?${params.toString()}`;
      const apiRes = await fetch(apiUrl);

      if (!apiRes.ok) {
        throw new Error('Failed to fetch tools');
      }

      const response = await apiRes.json();
      
      if (page === 1) {
        setTools(response.tools);
      } else {
        setTools(prev => [...prev, ...response.tools]);
      }
      
      setPagination(response.pagination);
      setHasMore(response.pagination.page < response.pagination.totalPages);
    } catch (error) {
      console.error('Error loading tools:', error);
      setHasMore(false);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  // 获取页面标题
  const getPageTitle = () => {
    if (searchQuery) {
      return `搜索结果: ${searchQuery}`;
    }
    if (categorySlug && categoryName) {
      return categoryT('allCategoryTools', { category: categoryName });
    }
    return t('allTools');
  };

  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen">
      {/* 面包屑导航 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-4">
          <div className="text-sm breadcrumbs mb-4">
            <ul className="flex items-center space-x-1">
              <li><Link href={`/${locale}`} className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary">{categoryT('home')}</Link></li>
              <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>

              {categorySlug ? (
                <>
                  <li>
                    <Link
                      href={`/${locale}/tools`}
                      className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary"
                    >
                      {categoryT('tools')}
                    </Link>
                  </li>
                  <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>
                  <li>
                    <Link
                      href={`/${locale}/categories`}
                      className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary"
                    >
                      {categoryT('categoryList')}
                    </Link>
                  </li>
                  <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>
                  <li className="text-primary font-medium">
                    {categoryName || categorySlug}
                  </li>
                </>
              ) : searchQuery ? (
                <>
                  <li>
                    <Link
                      href={`/${locale}/tools`}
                      className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary"
                    >
                      {categoryT('tools')}
                    </Link>
                  </li>
                  <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>
                  <li className="text-primary font-medium">搜索结果</li>
                </>
              ) : (
                <li className="text-primary font-medium">{t('allTools')}</li>
              )}
            </ul>
          </div>

          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-50">{getPageTitle()}</h1>
          {categorySlug && <p className="text-gray-600 dark:text-gray-300 mt-2">{categoryT('categoryDescription')}</p>}
          {searchQuery && <p className="text-gray-600 dark:text-gray-300 mt-2">找到 {pagination.total} 个相关工具</p>}
        </div>
      </div>

      {/* 分类导航 */}
      <CategoryNavigation locale={locale || 'en'} />

      {/* 筛选组件 */}
      <ToolsFilter locale={locale || 'en'} />

      <div className="container mx-auto py-8 px-4">
        {loading ? (
          <SkeletonGrid />
        ) : (
          <>
            {tools.length > 0 ? (
              <>
                <Suspense fallback={<SkeletonGrid />}>
                  <ToolsGrid tools={tools} locale={locale || 'en'} />
                </Suspense>
                
                {/* Observer target element - load more when scrolled to this element */}
                {hasMore && (
                  <div ref={observerTarget} className="mt-8">
                    {loadingMore && <LoadingIndicator />}
                  </div>
                )}
                
                {/* All loaded notification */}
                {!hasMore && tools.length > 0 && pagination.page >= pagination.totalPages && (
                  <div className="text-center text-gray-500 mt-8 py-4">
                    {t('allLoaded')}
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">
                  {searchQuery ? t('noSearchResults') :
                   categorySlug ? categoryT('noCategoryTools') : t('noTools')}
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
} 