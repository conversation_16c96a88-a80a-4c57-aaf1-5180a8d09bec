import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { prisma } from '@/lib/prisma';
import { unstable_setRequestLocale } from 'next-intl/server';
import { getTranslations } from 'next-intl/server';
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { ChevronRight, Heart, Share2, BarChart3, Globe, Users } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import Script from 'next/script';
import { generateSEOMetadata, generateToolSchema, cleanMetaDescription, extractKeywords } from '@/lib/seo';
import { ToolBreadcrumbs } from '@/components/SEOBreadcrumbs';
import type { Metadata } from 'next';

// 定义页面参数类型，使用与约束兼容的类型
type PageParams = {
  locale: string;
  id: string;
};

// 定义工具特性类型
interface ToolFeature {
  id: number;
  toolId: string;
  locale: string;
  feature: string;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// 定义翻译类型
interface Translation {
  id: number;
  locale: string;
  name: string;
  description: string;
  longDescription?: string;
}

// 定义分类关系类型
interface CategoryRelation {
  categoryId: number;
  category: {
    id: number;
    slug: string;
    translations: Translation[];
  };
  createdAt: Date | string;
}

// 定义标签关系类型
interface TagRelation {
  tagId: number;
  tag: {
    id: number;
    slug: string;
    translations: Translation[];
  };
  createdAt: Date | string;
}

// 定义工具类型
interface Tool {
  id: number;
  toolId: string;
  iconUrl: string;
  url: string;
  websiteLogo?: string | null;
  domainRegistrationDate?: Date | string | null;
  pricingType: string;
  isPremium: boolean;
  isNew: boolean;
  isFeatured: boolean;
  rating?: number | null;
  apiAvailable: boolean;
  publisher?: string | null;
  publisherUrl?: string | null;
  translations: Translation[];
  features: ToolFeature[];
  categories: CategoryRelation[];
  tags: TagRelation[];
  createdAt: Date | string;
  updatedAt: Date | string;
}


// Mock数据 - 使用统计
const mockStats = {
  monthlyVisits: 123456,
  userRating: 4.7,
  totalReviews: 421,
  countries: [
    { name: "美国", percent: 35.8 },
    { name: "中国", percent: 25.3 },
    { name: "印度", percent: 12.6 },
    { name: "英国", percent: 8.4 },
    { name: "其他", percent: 17.9 },
  ],
  userTypes: [
    { name: "学生", percent: 42 },
    { name: "创作者", percent: 28 },
    { name: "研究人员", percent: 15 },
    { name: "企业用户", percent: 10 },
    { name: "其他", percent: 5 },
  ],
  growth: [
    { month: "1月", users: 5000 },
    { month: "2月", users: 7500 },
    { month: "3月", users: 10000 },
    { month: "4月", users: 15000 },
    { month: "5月", users: 20000 },
    { month: "6月", users: 30000 },
  ],
};

// Mock数据 - 相关工具
const mockRelatedTools = [
  {
    id: 101,
    toolId: "related-tool-1",
    name: "相关AI工具1",
    description: "这是一个相关的AI工具，提供类似功能",
    iconUrl: "https://picsum.photos/id/111/200/200",
    rating: 4.2
  },
  {
    id: 102,
    toolId: "related-tool-2",
    name: "相关AI工具2",
    description: "另一个相关的AI工具，可以与主工具配合使用",
    iconUrl: "https://picsum.photos/id/222/200/200",
    rating: 4.5
  },
  {
    id: 103,
    toolId: "related-tool-3",
    name: "相关AI工具3",
    description: "第三个相关的AI工具，适合相似用途",
    iconUrl: "https://picsum.photos/id/333/200/200",
    rating: 4.0
  }
];

// 服务端获取工具数据
async function getToolById(id: string, locale: string): Promise<{ tool: Tool | null, relatedTools: Tool[] }> {
  try {
    // @ts-ignore
    const tool = await prisma.tool.findUnique({
      where: {
        toolId: id,
      },
      select: {
        id: true,
        toolId: true,
        iconUrl: true,
        url: true,
        websiteLogo: true,
        domainRegistrationDate: true,
        pricingType: true,
        isPremium: true,
        isNew: true,
        isFeatured: true,
        rating: true,
        apiAvailable: true,
        createdAt: true,
        translations: {
          where: {
            locale,
          },
          select: {
            id: true,
            locale: true,
            name: true,
            description: true,
            longDescription: true
          }
        },
        categories: {
          select: {
            categoryId: true,
            category: {
              select: {
                id: true,
                slug: true,
                translations: {
                  where: {
                    locale,
                  },
                  select: {
                    id: true,
                    locale: true,
                    name: true,
                    description: true
                  }
                },
              },
            },
          },
        },
        tags: {
          select: {
            tagId: true,
            tag: {
              select: {
                id: true,
                slug: true,
                translations: {
                  where: {
                    locale,
                  },
                  select: {
                    id: true,
                    locale: true,
                    name: true
                  }
                },
              },
            },
          },
        },
        features: {
          where: {
            locale,
          },
          select: {
            id: true,
            locale: true,
            feature: true
          }
        },
      },
    });
    
    if (!tool) {
      return { tool: null, relatedTools: [] };
    }
    
    // 获取相关工具
    // 策略：找出与当前工具相同分类或相同标签的工具
    let categoryIds: number[] = [];
    let tagIds: number[] = [];
    
    // 提取分类和标签ID
    if (tool.categories && tool.categories.length > 0) {
      categoryIds = tool.categories.map(cat => cat.categoryId);
    }
    
    if (tool.tags && tool.tags.length > 0) {
      tagIds = tool.tags.map(tag => tag.tagId);
    }
    
    // 查询相关工具
    // @ts-ignore
    const relatedTools = await prisma.tool.findMany({
      where: {
        OR: [
          {
            categories: {
              some: {
                categoryId: {
                  in: categoryIds
                }
              }
            }
          },
          {
            tags: {
              some: {
                tagId: {
                  in: tagIds
                }
              }
            }
          }
        ],
        // 排除当前工具
        NOT: {
          toolId: id
        }
      },
      take: 10, // 最多返回5个相关工具
      select: {
        id: true,
        toolId: true,
        iconUrl: true,
        url: true,
        rating: true,
        translations: {
          where: {
            locale,
          },
          select: {
            name: true,
            description: true
          }
        }
      },
      orderBy: {
        // 可以根据某些指标排序，如评分
        rating: 'desc'
      }
    });
    
    // 转换Decimal类型为普通JavaScript数字类型
    const serializedTool = {
      ...tool,
      // 将rating从Decimal转换为number
      rating: tool.rating ? parseFloat(tool.rating.toString()) : null,
    };
    
    // 序列化相关工具
    const serializedRelatedTools = relatedTools.map(relTool => ({
      ...relTool,
      rating: relTool.rating ? parseFloat(relTool.rating.toString()) : null,
    }));
    
    // 使用类型断言强制转换，绕过TypeScript详细的类型检查
    return { 
      tool: serializedTool as unknown as Tool, 
      relatedTools: serializedRelatedTools as unknown as Tool[]
    };
  } catch (error) {
    console.error('Error fetching tool:', error);
    return { tool: null, relatedTools: [] };
  }
}

// 评分星星组件 - 更新以支持暗色模式
function RatingStars({ rating }: { rating: number }) {
  return (
    <div className="flex items-center">
      {[1, 2, 3, 4, 5].map((star) => (
        <span key={star} className={`text-xl ${star <= rating ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`}>★</span>
      ))}
      <span className="ml-1 text-gray-700 dark:text-gray-300 font-medium">{rating.toFixed(1)}</span>
    </div>
  );
}

// 进度条组件
function ProgressBar({ percent, color }: { percent: number, color: string }) {
  return (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div
        className={`h-2 rounded-full ${color}`}
        style={{ width: `${percent}%` }}
      ></div>
    </div>
  );
}

// Generate metadata for tool detail page
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; id: string }>;
}): Promise<Metadata> {
  const { locale, id } = await params;

  try {
    const { tool } = await getToolById(id, locale);

    if (!tool) {
      return generateSEOMetadata({
        title: 'Tool Not Found',
        description: 'The requested AI tool could not be found.',
        locale
      });
    }

    const translation = tool.translations?.[0];

    if (!translation) {
      return generateSEOMetadata({
        title: 'Tool Not Found',
        description: 'The requested AI tool could not be found.',
        locale
      });
    }

    const cleanDescription = cleanMetaDescription(translation.longDescription || translation.description || '');
    const keywords = extractKeywords(
      `${translation.name || ''} ${translation.description || ''} ${tool.features?.map(f => f.feature).join(' ') || ''}`,
      tool.categories.map(c => c.category.translations?.[0]?.name).filter(Boolean)
    );

    return generateSEOMetadata({
      title: translation.name || 'AI Tool',
      description: cleanDescription,
      keywords,
      image: tool.iconUrl,
      url: `/tools/${tool.toolId}`,
      locale,
      type: 'article',
      author: tool.publisher || undefined
    });
  } catch (error) {
    console.error('Error generating metadata for tool:', error);
    return generateSEOMetadata({
      title: 'AI Tool',
      description: 'Discover AI tools and software on Aistak.',
      locale
    });
  }
}

export default async function ToolDetailPage({
  params,
}: {
  params: Promise<{ locale: string; id: string }>;
}) {
  // 使用 await 获取 locale 和 id
  const { locale, id } = await params;
  
  // 设置语言
  unstable_setRequestLocale(locale);

  // 获取国际化翻译
  const t = await getTranslations('tools.toolDetail');
  const toolsText = await getTranslations('tools');
  
  // 获取工具详情和相关工具
  const { tool, relatedTools } = await getToolById(id, locale);
  
  // 工具不存在则显示404
  if (!tool) {
    notFound();
  }
  
  // 获取当前语言的翻译
  const translation = tool.translations?.[0];

  // 如果没有翻译数据，显示404
  if (!translation) {
    notFound();
  }

  const ratingValue = tool.rating ? (typeof tool.rating === 'number' ? tool.rating : parseFloat(String(tool.rating))) : 4.5;

  return (
    <>
      {/* Structured Data */}
      <Script
        id="tool-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: generateToolSchema(tool, locale),
        }}
      />

      <div className="bg-gray-50 dark:bg-gray-900 min-h-screen pb-12">
      {/* 顶部信息栏 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-4">
          {/* SEO Breadcrumbs */}
          <div className="mb-4">
            <ToolBreadcrumbs tool={tool} locale={locale} />
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="w-16 h-16 bg-white dark:bg-gray-700 rounded-lg shadow-md p-1 mr-4 relative">
                <Image 
                  src={tool.websiteLogo || tool.iconUrl} 
                  alt={translation.name || 'AI Tool'}
                  fill
                  priority
                  sizes="64px"
                  quality={90}
                  className="rounded-md object-contain"
                />
              </div>
              <div>
                <h1 className="text-3xl font-bold mb-1 text-gray-900 dark:text-gray-50">{translation.name || 'AI Tool'}</h1>
                <p className="text-gray-500 dark:text-gray-400">{translation.description || ''}</p>
                <div className="flex items-center space-x-4">
                  <RatingStars rating={ratingValue} />
                  {/* <span className="text-gray-500 dark:text-gray-400">|</span>
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <span className="mr-2">0 {t('ratings')}</span>
                    <span>0 {t('favorites')}</span>
                  </div> */}
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" size="sm" className="flex items-center">
                <Heart size={16} className="mr-1" />
                {t('favorite')}
              </Button>
              <Button variant="outline" size="sm" className="flex items-center">
                <Share2 size={16} className="mr-1" />
                {t('share')}
              </Button>
              <Link href={tool.url} target="_blank">
                <Button className="flex items-center">
                  {t('openWebsite')}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
      
      <div className="container mx-auto px-4">
        <Tabs defaultValue="intro" className="w-full">
          <TabsContent value="intro">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* 左侧主内容区 */}
              <div className="lg:col-span-2">
                {/* 工具截图展示 - 使用iconUrl */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-8 overflow-hidden">
                  <div className="aspect-video relative bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                    <Image 
                      src={tool.iconUrl} 
                      alt={translation.name || 'AI Tool'}
                      fill
                      priority
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                      quality={85}
                      placeholder="blur"
                      blurDataURL="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 225' width='400' height='225'%3E%3Crect width='400' height='225' fill='%23f5f5f5'/%3E%3C/svg%3E"
                      className="object-contain p-1"
                    />
                  </div>
                </div>
              
                {/* 工具详情 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-8 p-6">
                  <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">{t('introduction')}</h2>
                  
                  {/* 工具详细描述 - 使用Markdown渲染 */}
                  <div className="prose dark:prose-invert max-w-none mb-6">
                    {translation.longDescription ? (
                      <div className="markdown-container border border-gray-200 dark:border-gray-700 rounded-lg p-5 bg-gray-50 dark:bg-gray-800/30">
                        <div className="markdown-content prose prose-headings:font-bold prose-headings:text-gray-900 dark:prose-headings:text-gray-100 prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-primary prose-a:no-underline hover:prose-a:underline prose-code:bg-gray-200 dark:prose-code:bg-gray-700 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-200 dark:prose-pre:bg-gray-700 prose-pre:p-4 prose-pre:rounded-lg prose-blockquote:border-l-4 prose-blockquote:border-gray-300 dark:prose-blockquote:border-gray-600 prose-blockquote:pl-4 prose-blockquote:italic prose-blockquote:text-gray-700 dark:prose-blockquote:text-gray-300 prose-ul:list-disc prose-ol:list-decimal">
                          <ReactMarkdown>
                            {translation.longDescription}
                          </ReactMarkdown>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-700 dark:text-gray-300">{translation.description || ''}</p>
                    )}
                  </div>
                  
                  {/* 功能特性 */}
                  {tool.features && tool.features.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-bold mb-3 text-gray-900 dark:text-white">{t('features')}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {tool.features.slice(0, 15).map((feature) => (
                          <div key={feature.id} className="flex items-start">
                            <div className="h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex items-center justify-center mr-2 mt-0.5">
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <span className="text-gray-700 dark:text-gray-300">{feature.feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  {/* 使用数据统计 */}
                  <div className="mt-8">
                    {/* 数据统计部分 */}
                  </div>
                </div>
              </div>
              
              {/* 右侧边栏 */}
              <div className="lg:col-span-1">
                {/* 工具信息卡片 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 mb-6">
                  <div className="mb-4">
                    <h3 className="font-bold mb-2 text-gray-900 dark:text-white">{t('toolsLibrary')}</h3>
                    <div className="text-sm text-gray-600 dark:text-gray-300">{translation.description || ''}</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                      <div className="text-sm text-gray-500 dark:text-gray-400">{t('type', { defaultValue: '类型' })}</div>
                      <div className="font-medium text-gray-800 dark:text-gray-200">
                        {tool.categories.length > 0 && tool.categories[0].category.translations.length > 0
                          ? tool.categories[0].category.translations[0].name
                          : t('generalAITool', { defaultValue: '通用AI工具' })}
                      </div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center">
                      <div className="text-sm text-gray-500 dark:text-gray-400">{t('price')}</div>
                      <div className="font-medium text-gray-800 dark:text-gray-200">
                        {tool.pricingType === 'freemium' && t('pricing.freemium')}
                        {tool.pricingType === 'paid' && t('pricing.paid')}
                        {tool.pricingType === 'subscription' && t('pricing.subscription')}
                        {!tool.pricingType || tool.pricingType === 'free' || 
                         (tool.pricingType !== 'freemium' && 
                          tool.pricingType !== 'paid' && 
                          tool.pricingType !== 'subscription') && t('pricing.free')}
                      </div>
                    </div>
                  </div>
                  
                  {/* 域名注册时间 - 新增字段展示 */}
                  {tool.domainRegistrationDate && (
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 text-center mb-4">
                      <div className="text-sm text-gray-500 dark:text-gray-400">{t('domainRegistrationDate', { defaultValue: '域名注册时间' })}</div>
                      <div className="font-medium text-gray-800 dark:text-gray-200">
                        {new Date(tool.domainRegistrationDate).toLocaleDateString()}
                      </div>
                    </div>
                  )}
                  
                  <Link href={tool.url} target="_blank" className="w-full">
                    <Button className="w-full mb-2">{t('visitWebsite')}</Button>
                  </Link>
                  
                  <Button variant="outline" className="w-full flex items-center justify-center">
                    <Heart size={16} className="mr-1" />
                    {t('favorite')}
                  </Button>
                </div>
                
                {/* 分类与标签 - 从左侧移到右侧 */}
                {(tool.categories.length > 0 || tool.tags.length > 0) && (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 mb-6">
                    <h3 className="font-bold mb-3 text-gray-900 dark:text-white">{t('categoriesAndTags')}</h3>
                    <div className="flex flex-wrap gap-2">
                      {tool.categories.slice(0, 15).map((cat) => (
                        cat.category.translations.length > 0 && (
                          <Link 
                            href={`/${locale}/tools?category=${cat.category.slug}`} 
                            key={cat.categoryId}
                          >
                            <Badge 
                              variant="secondary" 
                              className="bg-blue-50 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300 cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-800/50 transition-colors"
                            >
                              {cat.category.translations[0].name}
                            </Badge>
                          </Link>
                        )
                      ))}
                      {tool.tags && tool.tags.map((tag) => (
                        tag.tag.translations.length > 0 && (
                          <Badge key={tag.tagId} variant="outline" className="bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                            {tag.tag.translations[0].name}
                          </Badge>
                        )
                      ))}
                    </div>
                  </div>
                )}
                
                {/* 相关推荐工具 - 使用数据库查询结果 */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-5 mb-6">
                  <h3 className="font-bold mb-4 text-gray-900 dark:text-white">{t('relatedTools')}</h3>
                  <div className="space-y-4">
                    {relatedTools.length > 0 ? (
                      relatedTools.map((relatedTool) => {
                        // 确保有翻译数据
                        if (relatedTool.translations.length === 0) return null;
                        const relatedTranslation = relatedTool.translations[0];
                        
                        return (
                          <Link 
                            href={`/${locale}/tools/${relatedTool.toolId}`} 
                            key={relatedTool.id} 
                            className="flex flex-col hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors overflow-hidden"
                          >
                            <div className="w-full h-32 bg-white dark:bg-gray-700 shadow-sm relative">
                              <Image 
                                src={relatedTool.iconUrl} 
                                alt={relatedTranslation.name}
                                fill
                                loading="lazy"
                                sizes="100%"
                                quality={85}
                                placeholder="blur"
                                blurDataURL="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 200' width='400' height='200'%3E%3Crect width='400' height='200' fill='%23f5f5f5'/%3E%3C/svg%3E"
                                className="object-cover"
                              />
                            </div>
                            <div className="w-full p-3 flex justify-between items-center">
                              <h4 className="font-medium text-base truncate text-gray-800 dark:text-gray-200 mr-2">{relatedTranslation.name}</h4>
                              <div className="flex items-center shrink-0">
                                <span className="text-yellow-400 text-sm mr-1">★</span>
                                <span className="text-sm text-gray-600 dark:text-gray-400">
                                  {relatedTool.rating ? relatedTool.rating.toFixed(1) : "N/A"}
                                </span>
                              </div>
                            </div>
                          </Link>
                        );
                      })
                    ) : (
                      <p className="text-sm text-gray-500 dark:text-gray-400 text-center py-4">{t('noRelatedTools')}</p>
                    )}
                  </div>
                  
                  <div className="mt-4 text-center">
                    <Link href={`/${locale}/tools`} className="text-sm text-primary hover:underline">
                      {t('browseMoreTools')}
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="reviews">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">{t('reviews')}</h2>
              <div className="text-center py-8">
                <p className="text-gray-500 dark:text-gray-400">{t('noReviews')}</p>
                <p className="text-sm text-gray-400 dark:text-gray-500 mt-2">{t('beFirstReviewer')}</p>
                <Button className="mt-4">{t('writeReview')}</Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="stats">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">{t('usageData')}</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="flex items-center border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex items-center justify-center mr-3">
                    <BarChart3 size={24} />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">{mockStats.monthlyVisits.toLocaleString()}</div>
                    <div className="text-gray-500 dark:text-gray-400 text-sm">{t('monthlyVisits')}</div>
                  </div>
                </div>
                <div className="flex items-center border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-300 flex items-center justify-center mr-3">
                    <Globe size={24} />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">{mockStats.countries.length}+</div>
                    <div className="text-gray-500 dark:text-gray-400 text-sm">{t('usingCountries')}</div>
                  </div>
                </div>
                <div className="flex items-center border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 flex items-center justify-center mr-3">
                    <Users size={24} />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">{mockStats.userTypes.length}</div>
                    <div className="text-gray-500 dark:text-gray-400 text-sm">{t('userTypes')}</div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 地区分布 */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-5">
                  <h4 className="font-bold mb-4 text-gray-900 dark:text-white">{t('regionalDistribution')}</h4>
                  <div className="space-y-4">
                    {mockStats.countries.map((country, index) => (
                      <div key={index}>
                        <div className="flex justify-between mb-1 text-sm">
                          <span className="text-gray-700 dark:text-gray-300">{country.name}</span>
                          <span className="text-gray-700 dark:text-gray-300">{country.percent}%</span>
                        </div>
                        <ProgressBar percent={country.percent} color={
                          index === 0 ? "bg-blue-500" : 
                          index === 1 ? "bg-green-500" : 
                          index === 2 ? "bg-purple-500" : 
                          index === 3 ? "bg-amber-500" : "bg-gray-500"
                        } />
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* 用户类型 */}
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-5">
                  <h4 className="font-bold mb-4 text-gray-900 dark:text-white">{t('userTypesTitle')}</h4>
                  <div className="space-y-4">
                    {mockStats.userTypes.map((type, index) => (
                      <div key={index}>
                        <div className="flex justify-between mb-1 text-sm">
                          <span className="text-gray-700 dark:text-gray-300">{type.name}</span>
                          <span className="text-gray-700 dark:text-gray-300">{type.percent}%</span>
                        </div>
                        <ProgressBar percent={type.percent} color={
                          index === 0 ? "bg-pink-500" : 
                          index === 1 ? "bg-yellow-500" : 
                          index === 2 ? "bg-indigo-500" : 
                          index === 3 ? "bg-red-500" : "bg-gray-500"
                        } />
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="similar">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {mockRelatedTools.map((relatedTool) => (
                <Link href={`/${locale}/tools/${relatedTool.toolId}`} key={relatedTool.id} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-300">
                  <div className="p-4">
                    <div className="flex items-start">
                      <div className="w-12 h-12 bg-white dark:bg-gray-700 rounded-lg shadow-sm p-1 mr-3 relative">
                        <Image 
                          src={relatedTool.iconUrl} 
                          alt={relatedTool.name}
                          fill
                          loading="lazy"
                          sizes="40px"
                          quality={75}
                          placeholder="blur"
                          blurDataURL="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 40 40' width='40' height='40'%3E%3Crect width='40' height='40' fill='%23f5f5f5'/%3E%3C/svg%3E"
                          className="rounded object-contain"
                        />
                      </div>
                      <div>
                        <h3 className="font-medium truncate text-gray-900 dark:text-gray-100">{relatedTool.name}</h3>
                        <div className="flex items-center mt-1">
                          <span className="text-yellow-400 text-xs mr-1">★</span>
                          <span className="text-sm text-gray-600 dark:text-gray-400">{relatedTool.rating}</span>
                        </div>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-3 line-clamp-2">{relatedTool.description}</p>
                  </div>
                </Link>
              ))}
            </div>
            
            <div className="mt-8 text-center">
              <Link href={`/${locale}/tools`} className="inline-flex items-center text-primary hover:underline">
                {toolsText('viewMore')}
                <ChevronRight size={16} className="ml-1" />
              </Link>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      </div>
    </>
  );
}