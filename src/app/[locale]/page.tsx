import { getTranslations } from 'next-intl/server';
import GoogleOneTapWrapper from "@/components/GoogleOneTapWrapper";
import { ToolCard } from '@/components/ToolCard';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Suspense } from 'react';
import { getFromCache, setToCache } from '@/lib/cache';
import { executeDbOperation } from '@/lib/db-mutex';
import { prisma } from '@/lib/prisma';
import Home from '@/app/[locale]/home';

// 服务端获取工具数据，添加缓存
async function getTools(locale: string) {
  // 使用缓存键，基于locale和时间
  const cacheKey = `tools_latest_${locale}_${Math.floor(Date.now() / (1000 * 60 * 10))}`; // 10分钟缓存

  // 检查缓存
  const cachedData = await getFromCache(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  // 使用互斥锁确保数据库操作串行执行
  const result = await executeDbOperation(
    async () => {
      return await prisma.$transaction(async (tx) => {
        // 串行执行查询以避免连接冲突
        const tools = await tx.tool.findMany({
          take: 24,
          orderBy: {
            updatedAt: 'desc',
          },
          select: {
            id: true,
            toolId: true,
            iconUrl: true,
            url: true,
            pricingType: true,
            isPremium: true,
            isNew: true,
            isFeatured: true,
            rating: true,
            createdAt: true,
            translations: {
              where: {
                locale,
              },
              select: {
                name: true,
                description: true,
              }
            },
            // 仅获取需要的分类信息
            categories: {
              take: 3, // 限制返回的分类数量
              select: {
                category: {
                  select: {
                    slug: true,
                    translations: {
                      where: {
                        locale,
                      },
                      select: {
                        name: true,
                      }
                    },
                  },
                },
              },
            },
          },
        });

        const total = await tx.tool.count();

        return {
          tools: tools.map((tool: any) => ({
            ...tool,
            rating: tool.rating ? parseFloat(tool.rating.toString()) : null,
            createdAt: tool.createdAt.toISOString(),
          })),
          total
        };
      }, { timeout: 30000 });
    },
    { tools: [], total: 0 }, // fallback
    'Error fetching tools'
  );

  // 存入缓存
  await setToCache(cacheKey, result, 60 * 10); // 10分钟

  return result;
}

// 根据分类slug获取分类下的工具
async function getToolsByCategory(categorySlug: string, locale: string, limit: number = 12) {
  // 使用缓存键，基于categorySlug、locale和时间
  const cacheKey = `tools_category_${categorySlug}_${locale}_${Math.floor(Date.now() / (1000 * 60 * 10))}`; // 10分钟缓存

  // 检查缓存
  const cachedData = await getFromCache(cacheKey);
  if (cachedData) {
    return cachedData;
  }

  // 使用互斥锁确保数据库操作串行执行
  const result = await executeDbOperation(
    async () => {
      return await prisma.$transaction(async (tx) => {
        // 首先获取分类信息
        const categoryData = await tx.$queryRaw<any[]>`
          SELECT c.id, c.slug, c.icon_url as "iconUrl", c.level,
                 ct.name, ct.description
          FROM categories c
          LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
          WHERE c.slug = ${categorySlug}
        `;

        if (!categoryData || categoryData.length === 0) {
          return { tools: [], total: 0, category: null };
        }

        const category = {
          ...categoryData[0],
          name: categoryData[0].name || categorySlug,
          description: categoryData[0].description || ''
        };

        const categoryId = categoryData[0].id;
        const isParentCategory = categoryData[0].level === 1;

        // 如果是父分类，获取子分类ID
        let categoryIds = [categoryId];
        if (isParentCategory) {
          const subcategories = await tx.$queryRaw<any[]>`
            SELECT id FROM categories WHERE parent_id = ${categoryId}
          `;
          categoryIds = [...categoryIds, ...subcategories.map((sub: any) => sub.id)];
        }

        // 串行获取工具和计数
        const tools = await tx.tool.findMany({
          take: limit,
          where: {
            categories: {
              some: {
                categoryId: {
                  in: categoryIds
                }
              }
            }
          },
          orderBy: { updatedAt: 'desc' },
          select: {
            id: true,
            toolId: true,
            iconUrl: true,
            url: true,
            pricingType: true,
            isPremium: true,
            isNew: true,
            isFeatured: true,
            rating: true,
            createdAt: true,
            translations: {
              where: { locale },
              select: {
                name: true,
                description: true,
              }
            },
            // 减少关联数据加载
            categories: {
              take: 1,
              select: {
                category: {
                  select: {
                    slug: true,
                  }
                },
              },
            }
          },
        });

        const count = await tx.toolCategory.count({
          where: {
            categoryId: {
              in: categoryIds
            }
          }
        });

        // 序列化工具数据
        const serializedTools = tools.map((tool: any) => ({
          ...tool,
          rating: tool.rating ? parseFloat(tool.rating.toString()) : null,
          createdAt: tool.createdAt.toISOString(),
        }));

        return {
          tools: serializedTools,
          total: count,
          category
        };
      }, { timeout: 30000 });
    },
    { tools: [], total: 0, category: null }, // fallback
    `Error fetching tools for category ${categorySlug}`
  );

  // 存入缓存
  await setToCache(cacheKey, result, 60 * 10); // 10分钟

  return result;
}

// 使用React缓存函数缓存getTools结果
export const revalidate = 3600; // 1小时重新验证一次数据

// 工具卡片网格组件，使用Suspense包裹
function ToolsGrid({ tools, locale }: { tools: any[], locale: string }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
      {tools.map((tool: any) => (
        <ToolCard key={tool.toolId} tool={tool} locale={locale} />
      ))}
    </div>
  );
}

// 分类工具展示区域组件
function CategorySection({
  category,
  tools,
  categorySlug,
  locale,
  t,
  fallbackTitle
}: {
  category: any,
  tools: any[],
  categorySlug: string,
  locale: string,
  t: any,
  fallbackTitle: string
}) {
  if (!category) return null;

  return (
    <div className="relative w-full my-4">
      <div className="flex items-center justify-between mb-4">
        <div className="inline-flex items-center px-4 py-2 rounded-full bg-secondary text-secondary-foreground border border-border/50">
          <h2 className="text-lg font-semibold">{category.name || fallbackTitle}</h2>
        </div>
        <Link href={`/${locale}/tools?category=${categorySlug}`} passHref>
          <Button className="bg-transparent hover:bg-primary text-primary hover:text-white border border-primary px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
            {t('viewMore')}
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
            </svg>
          </Button>
        </Link>
      </div>

      {tools.length === 0 ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-gray-500">{t('noTools', { defaultValue: '暂无工具数据' })}</p>
        </div>
      ) : (
        <>
          <Suspense fallback={
            <div className="animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
              {[...Array(12)].map((_, index) => (
                <div key={index} className="bg-gray-100 rounded-lg h-64"></div>
              ))}
            </div>
          }>
            <ToolsGrid tools={tools} locale={locale} />
          </Suspense>
        </>
      )}
    </div>
  );
}

export default async function LandingPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  // 使用 await 获取 locale
  const { locale } = await params;

  // 获取国际化翻译
  const t = await getTranslations('tools');

  // 串行获取数据以避免数据库连接冲突
  let latestToolsData, writingCategoryData, imageCategoryData, videoCategoryData, voiceCategoryData,
      marketingCategoryData, productivityCategoryData, chatbotCategoryData, educationCategoryData,
      businessCategoryData, designArtCategoryData, codeItCategoryData;

  try {
    // 串行执行，避免并发连接问题
    latestToolsData = await getTools(locale);
    writingCategoryData = await getToolsByCategory('text-writing', locale);
    imageCategoryData = await getToolsByCategory('image', locale);
    videoCategoryData = await getToolsByCategory('video', locale);
    voiceCategoryData = await getToolsByCategory('voice', locale);
    marketingCategoryData = await getToolsByCategory('marketing', locale);
    productivityCategoryData = await getToolsByCategory('productivity', locale);
    chatbotCategoryData = await getToolsByCategory('chatbot', locale);
    educationCategoryData = await getToolsByCategory('education', locale);
    businessCategoryData = await getToolsByCategory('business', locale);
    designArtCategoryData = await getToolsByCategory('design-art', locale);
    codeItCategoryData = await getToolsByCategory('code-it', locale);
  } catch (error) {
    console.error('Error fetching page data:', error);
    // 提供默认数据以防止页面崩溃
    latestToolsData = { tools: [], total: 0 };
    writingCategoryData = { tools: [], total: 0, category: null };
    imageCategoryData = { tools: [], total: 0, category: null };
    videoCategoryData = { tools: [], total: 0, category: null };
    voiceCategoryData = { tools: [], total: 0, category: null };
    marketingCategoryData = { tools: [], total: 0, category: null };
    productivityCategoryData = { tools: [], total: 0, category: null };
    chatbotCategoryData = { tools: [], total: 0, category: null };
    educationCategoryData = { tools: [], total: 0, category: null };
    businessCategoryData = { tools: [], total: 0, category: null };
    designArtCategoryData = { tools: [], total: 0, category: null };
    codeItCategoryData = { tools: [], total: 0, category: null };
  }

  // 解构数据
  const { tools } = latestToolsData;
  const { tools: writingTools, category: writingCategory } = writingCategoryData;
  const { tools: imageTools, category: imageCategory } = imageCategoryData;
  const { tools: videoTools, category: videoCategory } = videoCategoryData;
  const { tools: voiceTools, category: voiceCategory } = voiceCategoryData;
  const { tools: marketingTools, category: marketingCategory } = marketingCategoryData;
  const { tools: productivityTools, category: productivityCategory } = productivityCategoryData;
  const { tools: chatbotTools, category: chatbotCategory } = chatbotCategoryData;
  const { tools: educationTools, category: educationCategory } = educationCategoryData;
  const { tools: businessTools, category: businessCategory } = businessCategoryData;
  const { tools: designArtTools, category: designArtCategory } = designArtCategoryData;
  const { tools: codeItTools, category: codeItCategory } = codeItCategoryData;

  return (
    <>
      {/* Google One Tap组件 */}
      <GoogleOneTapWrapper />
      
      {/* Hero区域和搜索 */}
      <Home />
      
      {/* 导航标签区域 */}
      <div className="sticky z-[5] bg-background/95 backdrop-blur-sm">
        <div className="lg:container mx-auto px-5 lg:px-4 xl:px-1 py-3">
          <div className="nav-tags-wrapper">
            <div className="flex overflow-x-auto py-2 gap-2 sm:gap-3 nav-tags-container">
            <Link href={`/${locale}/tools`} className="category-tag">
              {t('allTools', { defaultValue: '全部工具' })}
            </Link>

            <Link href={`/${locale}/tools?sort=newest-registered`} className="category-tag flex items-center gap-1.5">
              <span className="relative flex h-2 w-2">
                <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-green-400 opacity-75"></span>
                <span className="relative inline-flex rounded-full h-2 w-2 bg-green-500"></span>
              </span>
              {t('latestRegisteredTools', { defaultValue: '最新注册' })}
            </Link>

            {writingCategory && (
              <Link href={`/${locale}/tools?category=text-writing`} className="category-tag">
                {writingCategory.name || (locale === 'zh' ? '文本与写作' : 'Text & Writing')}
              </Link>
            )}

            {imageCategory && (
              <Link href={`/${locale}/tools?category=image`} className="category-tag">
                {imageCategory.name || (locale === 'zh' ? '图像' : 'Image')}
              </Link>
            )}

            {videoCategory && (
              <Link href={`/${locale}/tools?category=video`} className="category-tag">
                {videoCategory.name || (locale === 'zh' ? '视频' : 'Video')}
              </Link>
            )}

            {voiceCategory && (
              <Link href={`/${locale}/tools?category=voice`} className="category-tag">
                {voiceCategory.name || (locale === 'zh' ? '语音' : 'Voice')}
              </Link>
            )}

            {marketingCategory && (
              <Link href={`/${locale}/tools?category=marketing`} className="category-tag">
                {marketingCategory.name || (locale === 'zh' ? '营销' : 'Marketing')}
              </Link>
            )}

            {productivityCategory && (
              <Link href={`/${locale}/tools?category=productivity`} className="category-tag">
                {productivityCategory.name || (locale === 'zh' ? '生产力' : 'Productivity')}
              </Link>
            )}

            {chatbotCategory && (
              <Link href={`/${locale}/tools?category=chatbot`} className="category-tag">
                {chatbotCategory.name || (locale === 'zh' ? '聊天机器人' : 'Chatbot')}
              </Link>
            )}

            {educationCategory && (
              <Link href={`/${locale}/tools?category=education`} className="category-tag">
                {educationCategory.name || (locale === 'zh' ? '教育' : 'Education')}
              </Link>
            )}

            {businessCategory && (
              <Link href={`/${locale}/tools?category=business`} className="category-tag">
                {businessCategory.name || (locale === 'zh' ? '商业' : 'Business')}
              </Link>
            )}

            {designArtCategory && (
              <Link href={`/${locale}/tools?category=design-art`} className="category-tag">
                {designArtCategory.name || (locale === 'zh' ? '设计与艺术' : 'Design & Art')}
              </Link>
            )}

            {codeItCategory && (
              <Link href={`/${locale}/tools?category=code-it`} className="category-tag">
                {codeItCategory.name || (locale === 'zh' ? '代码与IT' : 'Code & IT')}
              </Link>
            )}

            {/* 更多分类按钮 */}
            <Link href={`/${locale}/categories`} className="category-tag border border-dashed border-border hover:border-primary/50 text-muted-foreground hover:text-foreground flex items-center gap-1">
              <span>{t('moreCategories')}</span>
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4"></path>
              </svg>
            </Link>
            </div>
          </div>
        </div>
      </div>

      {/* 添加一个空的div占位元素，高度等于导航栏高度 */}
      {/* <div className="h-[56px]"></div> */}

      <div className="relative w-full my-4">
        <div className="flex items-center justify-between mb-4">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-secondary text-secondary-foreground border border-border/50">
            <h1 className="text-lg font-semibold">{t('latestTools')}</h1>
          </div>
          <Link href={`/${locale}/tools`} passHref>
            <Button className="bg-transparent hover:bg-primary text-primary hover:text-white border border-primary px-6 py-2 rounded-lg transition-all duration-300 flex items-center gap-2">
              {t('viewMore')}
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
              </svg>
            </Button>
          </Link>
        </div>
        
        {tools.length === 0 ? (
          <div className="flex justify-center items-center h-64">
            <p className="text-gray-500">{t('noTools', { defaultValue: '暂无工具数据' })}</p>
          </div>
        ) : (
          <>
            <Suspense fallback={
              <div className="animate-pulse grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                {[...Array(12)].map((_, index) => (
                  <div key={index} className="bg-gray-100 rounded-lg h-64"></div>
                ))}
              </div>
            }>
              <ToolsGrid tools={tools} locale={locale} />
            </Suspense>
          </>
        )}
        
        {/* 分类工具展示区域 */}
        <CategorySection
          category={writingCategory}
          tools={writingTools}
          categorySlug="text-writing"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '文本与写作' : 'Text & Writing'}
        />

        <CategorySection
          category={imageCategory}
          tools={imageTools}
          categorySlug="image"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '图像' : 'Image'}
        />

        <CategorySection
          category={videoCategory}
          tools={videoTools}
          categorySlug="video"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '视频' : 'Video'}
        />

        <CategorySection
          category={voiceCategory}
          tools={voiceTools}
          categorySlug="voice"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '语音' : 'Voice'}
        />

        <CategorySection
          category={marketingCategory}
          tools={marketingTools}
          categorySlug="marketing"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '营销' : 'Marketing'}
        />

        <CategorySection
          category={productivityCategory}
          tools={productivityTools}
          categorySlug="productivity"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '生产力' : 'Productivity'}
        />

        <CategorySection
          category={chatbotCategory}
          tools={chatbotTools}
          categorySlug="chatbot"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '聊天机器人' : 'Chatbot'}
        />

        <CategorySection
          category={educationCategory}
          tools={educationTools}
          categorySlug="education"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '教育' : 'Education'}
        />

        <CategorySection
          category={businessCategory}
          tools={businessTools}
          categorySlug="business"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '商业' : 'Business'}
        />

        <CategorySection
          category={designArtCategory}
          tools={designArtTools}
          categorySlug="design-art"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '设计与艺术' : 'Design & Art'}
        />

        <CategorySection
          category={codeItCategory}
          tools={codeItTools}
          categorySlug="code-it"
          locale={locale}
          t={t}
          fallbackTitle={locale === 'zh' ? '代码与IT' : 'Code & IT'}
        />

      </div>
    </>
  );
}


