import { useTranslations } from 'next-intl';
import { generateSEOMetadata } from '@/lib/seo';
import { routing } from '@/i18n/routing';
import type { Metadata } from 'next';

interface TermsPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateStaticParams() {
  return routing.locales.map((locale) => ({locale}));
}

export async function generateMetadata({ params }: TermsPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  return generateSEOMetadata({
    title: locale === 'zh' ? '服务条款 - AISTAK' : 'Terms of Service - AISTAK',
    description: locale === 'zh'
      ? 'AISTAK 服务条款 - 了解使用我们AI工具目录平台的条款和条件。'
      : 'AISTAK Terms of Service - Learn about the terms and conditions for using our AI tools directory platform.',
    url: `/${locale}/terms`,
    locale,
  });
}

export default function TermsPage() {
  const t = useTranslations('terms');

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-16 max-w-4xl">
        <div className="prose prose-gray dark:prose-invert max-w-none">
          <h1 className="text-4xl font-bold text-foreground mb-8">
            {t('title')}
          </h1>
          
          <div className="text-muted-foreground mb-8">
            <p className="text-lg leading-relaxed">
              {t('welcome')}
            </p>
          </div>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section1.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section1.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section2.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section2.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section3.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section3.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section4.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section4.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('section5.title')}
            </h2>
            <p className="text-muted-foreground mb-4 leading-relaxed">
              {t('section5.content')}
            </p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold text-foreground mb-4">
              {t('contact.title')}
            </h2>
            <p className="text-muted-foreground leading-relaxed">
              {t('contact.content')}{' '}
              <a 
                href="mailto:<EMAIL>" 
                className="text-primary hover:underline"
              >
                <EMAIL>
              </a>
            </p>
          </section>
        </div>
      </div>
    </div>
  );
}
