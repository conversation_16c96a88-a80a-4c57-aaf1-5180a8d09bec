import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { unstable_setRequestLocale } from 'next-intl/server';
import { getTranslations } from 'next-intl/server';
import { ChevronRight } from 'lucide-react';
import * as Icons from 'lucide-react';
import { getMainCategories } from '@/constants/categoryConfig';
import type { MainCategory, SubCategory } from '@/constants/categoryConfig';

// 二级分类卡片组件
function SubCategoryCard({ category, locale }: { category: SubCategory, locale: string }) {
  // 默认图标URL，如果分类没有图标则使用
  const defaultIconUrl = 'https://picsum.photos/id/237/200/200';
  
  return (
    <Link 
      href={`/${locale}/tools?category=${category.slug}`} 
      className="flex items-center gap-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:shadow-md transition-shadow p-4 border border-gray-200 dark:border-gray-700"
    >
      {/* <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg relative flex-shrink-0">
        <Image 
          src={category.iconUrl || defaultIconUrl}
          alt={category.name}
          fill
          sizes="40px"
          className="object-cover rounded-lg"
          loading="lazy"
        />
      </div> */}
      <div className="flex-1 min-w-0">
        <h3 className="font-medium text-base text-gray-900 dark:text-white truncate">{category.name}</h3>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          Count:{category.toolCount || 0}
        </div>
      </div>
    </Link>
  );
}

// 获取图标组件
function getCategoryIcon(iconName: string | null) {
  if (!iconName) return <Icons.Folder className="w-6 h-6 text-primary mr-2" />;
  
  try {
    // 首字母大写，保持其他字母不变
    const formattedName = iconName.charAt(0).toUpperCase() + iconName.slice(1);
    
    // 预定义图标映射，避免动态属性访问导致的类型错误
    switch (formattedName) {
      case 'PenLine':
        return <Icons.PenLine className="w-6 h-6 text-primary mr-2" />;
      case 'Image':
        return <Icons.Image className="w-6 h-6 text-primary mr-2" />;
      case 'Music':
        return <Icons.Music className="w-6 h-6 text-primary mr-2" />;
      case 'Video':
        return <Icons.Video className="w-6 h-6 text-primary mr-2" />;
      case 'Code':
        return <Icons.Code className="w-6 h-6 text-primary mr-2" />;
      case 'Briefcase':
        return <Icons.Briefcase className="w-6 h-6 text-primary mr-2" />;
      case 'Building2':
        return <Icons.Building2 className="w-6 h-6 text-primary mr-2" />;
      case 'GraduationCap':
        return <Icons.GraduationCap className="w-6 h-6 text-primary mr-2" />;
      case 'Heart':
        return <Icons.Heart className="w-6 h-6 text-primary mr-2" />;
      case 'Palette':
        return <Icons.Palette className="w-6 h-6 text-primary mr-2" />;
      case 'MessageSquare':
        return <Icons.MessageSquare className="w-6 h-6 text-primary mr-2" />;
      case 'Search':
        return <Icons.Search className="w-6 h-6 text-primary mr-2" />;
      case 'BarChart':
        return <Icons.BarChart className="w-6 h-6 text-primary mr-2" />;
      case 'Box':
        return <Icons.Box className="w-6 h-6 text-primary mr-2" />;
      case 'Sparkles':
        return <Icons.Sparkles className="w-6 h-6 text-primary mr-2" />;
      case 'ClipboardList':
        return <Icons.ClipboardList className="w-6 h-6 text-primary mr-2" />;
      case 'Layers':
        return <Icons.Layers className="w-6 h-6 text-primary mr-2" />;
      case 'Mic':
        return <Icons.Mic className="w-6 h-6 text-primary mr-2" />;
      default:
        return <Icons.Folder className="w-6 h-6 text-primary mr-2" />;
    }
  } catch (error) {
    // 任何错误都使用默认图标
    return <Icons.Folder className="w-6 h-6 text-primary mr-2" />;
  }
}

// 一级分类和其子分类组件
function MainCategorySection({ category, locale }: { category: MainCategory, locale: string }) {
  return (
    <div className="mb-12">
      <div className="flex items-center mb-6">
        {getCategoryIcon(category.mainCategory)}
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          {category.name}
        </h2>
      </div>
      
      {category.children && category.children.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {category.children.map((subCategory) => (
            <SubCategoryCard 
              key={subCategory.id} 
              category={subCategory} 
              locale={locale} 
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <p className="text-gray-500 dark:text-gray-400">暂无子分类</p>
        </div>
      )}
    </div>
  );
}

export default async function CategoryListPage({ 
  params 
}: { 
  params: Promise<{ locale: string}>;
}) {
  // 获取locale
  const { locale } = await params;
  
  // 设置语言
  unstable_setRequestLocale(locale);

  // 获取国际化翻译
  const t = await getTranslations('categories');
  
  // 从数据库获取所有分类
  const categories = await getMainCategories(locale);
  
  return (
    <div className="bg-gray-50 dark:bg-gray-900 min-h-screen pb-12">
      {/* 顶部信息栏 */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="text-sm breadcrumbs mb-4">
            <ul className="flex items-center space-x-1">
              <li><Link href={`/${locale}`} className="text-gray-500 dark:text-gray-400 hover:text-primary dark:hover:text-primary">{t('home')}</Link></li>
              <li><ChevronRight size={14} className="text-gray-400 dark:text-gray-500" /></li>
              <li className="text-primary font-medium">{t('categoryList')}</li>
            </ul>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-50">{t('categoryList')}</h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">{t('categoryDescription')}</p>
        </div>
      </div>
      
      <div className="container mx-auto px-4 py-8">
        {categories.length > 0 ? (
          <div className="space-y-12">
            {categories.map(category => (
              <MainCategorySection 
                key={category.id} 
                category={category} 
                locale={locale} 
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">{t('noCategories')}</p>
          </div>
        )}
      </div>
    </div>
  );
} 