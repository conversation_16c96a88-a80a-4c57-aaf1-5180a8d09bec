@import './theme.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .tool-card {
    @apply relative bg-card border border-border p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card:hover {
    @apply shadow-md border-primary/20 -translate-y-1;
  }

  .tool-card-featured {
    @apply relative bg-gradient-to-br from-accent to-background border border-primary/20 p-5 rounded-xl transition-all duration-300;
  }
  
  .tool-card-featured:hover {
    @apply shadow-lg border-primary/40 -translate-y-1;
  }

  .badge {
    @apply inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .badge-new {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-featured {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .badge-free {
    @apply badge bg-green-500 text-white border-transparent;
  }

  .badge-premium {
    @apply badge bg-amber-500 text-white border-transparent;
  }

  .nav-link {
    @apply flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground;
  }

  .nav-link-active {
    @apply bg-accent text-accent-foreground;
  }

  .search-input {
    @apply w-full bg-background/70 backdrop-blur-sm rounded-full px-5 py-3 border border-border focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-base;
  }

  .glass-card {
    @apply bg-white/70 backdrop-blur-sm border border-white/20 shadow-sm;
  }

  .glass-effect {
    @apply bg-white/80 backdrop-blur-md;
  }
  
  /* Improved category button styles */
  .category-button {
    @apply flex items-center gap-2 px-4 py-3 rounded-xl border border-border bg-card hover:border-primary/30 hover:shadow-sm transition-all;
  }
  
  .category-button-active {
    @apply border-primary/50 bg-accent shadow-sm;
  }
  
  /* Better form controls */
  .form-input {
    @apply rounded-md border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all;
  }
  
  /* Animation utilities */
  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-lift {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-md;
  }

  /* Navigation tags scrolling */
  .nav-tags-container {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .nav-tags-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  /* Category tag styles */
  .category-tag {
    @apply px-3 py-2 sm:px-4 whitespace-nowrap rounded-full bg-secondary text-secondary-foreground hover:bg-accent hover:text-accent-foreground transition-colors duration-200 flex-shrink-0 text-sm font-medium;
  }

  .category-tag-active {
    @apply bg-primary text-primary-foreground hover:bg-primary/90;
  }

  /* Smooth scrolling for navigation tags */
  .nav-tags-container {
    scroll-behavior: smooth;
  }

  /* Add fade effect at edges for better UX */
  .nav-tags-wrapper {
    position: relative;
  }

  .nav-tags-wrapper::before,
  .nav-tags-wrapper::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 20px;
    pointer-events: none;
    z-index: 1;
  }

  .nav-tags-wrapper::before {
    left: 0;
    background: linear-gradient(to right, hsl(var(--background)), transparent);
  }

  .nav-tags-wrapper::after {
    right: 0;
    background: linear-gradient(to left, hsl(var(--background)), transparent);
  }
}

/* Markdown Content Styling */
.markdown-content {
  @apply text-base text-gray-800 dark:text-gray-200;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  @apply font-bold mb-4 mt-6;
}

.markdown-content h1 {
  @apply text-2xl;
}

.markdown-content h2 {
  @apply text-xl;
}

.markdown-content h3 {
  @apply text-lg;
}

.markdown-content p {
  @apply mb-4 last:mb-0;
}

.markdown-content ul {
  @apply list-disc pl-6 space-y-2 mb-4;
}

.markdown-content ol {
  @apply list-decimal pl-6 space-y-2 mb-4;
}

.markdown-content a {
  @apply text-primary hover:underline;
}

.markdown-content code {
  @apply bg-gray-200 dark:bg-gray-700 rounded px-1 py-0.5;
}

.markdown-content pre {
  @apply bg-gray-200 dark:bg-gray-700 p-4 rounded-lg overflow-x-auto mb-4;
}

.markdown-content blockquote {
  @apply border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic text-gray-700 dark:text-gray-300 mb-4;
}

.markdown-content img {
  @apply rounded-lg max-w-full h-auto mx-auto my-4;
}

.markdown-content table {
  @apply w-full border-collapse mb-4;
}

.markdown-content table th,
.markdown-content table td {
  @apply border border-gray-300 dark:border-gray-600 p-2;
}

.markdown-content table th {
  @apply bg-gray-100 dark:bg-gray-800;
}
