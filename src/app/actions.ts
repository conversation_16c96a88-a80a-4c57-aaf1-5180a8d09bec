export async function getLandingPage(locale: string) {
  try {
    let messages;
    try {
      messages = (await import(`../../messages/${locale}.json`)).default;
    } catch {
      // If the requested locale file doesn't exist, fallback to English
      messages = (await import(`../../messages/en.json`)).default;
    }
    return {
      header: messages.header,
      footer: messages.footer,
      hero: messages.hero,
      branding: messages.branding,
      introduce: messages.introduce,
      benefit: messages.benefit,
      usage: messages.usage,
      feature: messages.feature,
      showcase: messages.showcase,
      stats: messages.stats,
      pricing: messages.pricing,
      testimonial: messages.testimonial,
      faq: messages.faq,
      cta: messages.cta
    };
  } catch (error) {
    console.error('Error loading landing page data:', error);
    throw error;
  }
}

// 定义工具相关类型
export type ToolTranslation = {
  locale: string;
  name: string;
  description: string;
  longDescription?: string;
};

export type Tool = {
  id: number;
  toolId: string;
  iconUrl: string;
  url: string;
  pricingType: string;
  isPremium: boolean;
  isNew: boolean;
  isFeatured: boolean;
  rating?: number;
  publisher?: string;
  translations: ToolTranslation[];
};

export type ToolsResponse = {
  tools: Tool[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
};

// 获取工具列表，添加缓存控制
export async function getTools(
  locale: string, 
  page: number = 1, 
  limit: number = 20, 
  category?: string
): Promise<ToolsResponse> {
  try {
    // 构建基本URL
    let url = `/api/tools?locale=${locale}&page=${page}&limit=${limit}`;
    
    // 如果有分类参数，添加到URL中
    if (category) {
      url += `&category=${encodeURIComponent(category)}`;
    }
    
    const response = await fetch(
      url,
      {
        next: { revalidate: 600 } // 10分钟缓存
      }
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch tools');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching tools:', error);
    return {
      tools: [],
      pagination: {
        total: 0,
        page,
        limit,
        totalPages: 0,
      }
    };
  }
}
