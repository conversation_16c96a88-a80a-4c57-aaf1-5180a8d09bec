/**
 * 分类数据接口定义
 * 提供类型支持，但实际数据从数据库获取
 */
import { prisma } from '@/lib/prisma';

// 一级分类接口
export interface MainCategory {
  id: number;          // 分类ID
  slug: string;        // 分类标识符
  iconUrl: string | null; // 图标地址
  mainCategory: string | null; // 图标名称
  weight: number;      // 排序权重
  name: string;        // 分类名称（从翻译中获取）
  description: string | null; // 分类描述（从翻译中获取）
  children?: SubCategory[]; // 子分类列表
  toolCount: number;   // 工具数量
}

// 二级分类接口
export interface SubCategory {
  id: number;          // 分类ID
  slug: string;        // 分类标识符
  iconUrl: string | null; // 图标地址
  weight: number;      // 排序权重
  name: string;        // 分类名称（从翻译中获取）
  description: string | null; // 分类描述（从翻译中获取）
  parentId: number | null; // 父分类ID
  toolCount: number;   // 工具数量
}

/**
 * 获取所有主分类（一级分类）及其子分类
 * @param locale 语言代码
 */
export async function getMainCategories(locale: string): Promise<MainCategory[]> {
  try {
    // 获取一级分类
    const mainCategories = await prisma.$queryRaw<any[]>`
      SELECT 
        c.id, c.slug, c.icon_url as "iconUrl", c.main_category as "mainCategory", 
        c.weight, c.created_at as "createdAt", c.updated_at as "updatedAt",
        ct.name, ct.description,
        (SELECT COUNT(*) FROM tool_categories tc WHERE tc.category_id = c.id) as "toolCount"
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
      WHERE c.level = 1
      ORDER BY c.weight DESC
    `;

    // 为每个一级分类获取其子分类
    const categoriesWithChildren: MainCategory[] = [];
    
    for (const main of mainCategories) {
      // 获取子分类
      const subCategories = await prisma.$queryRaw<any[]>`
        SELECT 
          c.id, c.slug, c.icon_url as "iconUrl", c.weight, 
          c.parent_id as "parentId", ct.name, ct.description,
          (SELECT COUNT(*) FROM tool_categories tc WHERE tc.category_id = c.id) as "toolCount"
        FROM categories c
        LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
        WHERE c.level = 2 AND c.parent_id = ${main.id}
        ORDER BY c.weight DESC
      `;
      
      // 添加到结果集
      categoriesWithChildren.push({
        ...main,
        toolCount: parseInt(main.toolCount) || 0,
        children: subCategories.map(sub => ({
          ...sub,
          toolCount: parseInt(sub.toolCount) || 0
        }))
      });
    }

    return categoriesWithChildren;
  } catch (error) {
    console.error('Error fetching categories:', error);
    return [];
  }
}

/**
 * 获取所有二级分类的平面列表
 * @param locale 语言代码
 */
export async function getAllSubcategories(locale: string): Promise<SubCategory[]> {
  try {
    const subCategories = await prisma.$queryRaw<any[]>`
      SELECT 
        c.id, c.slug, c.icon_url as "iconUrl", c.weight, 
        c.parent_id as "parentId", ct.name, ct.description,
        (SELECT COUNT(*) FROM tool_categories tc WHERE tc.category_id = c.id) as "toolCount"
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
      WHERE c.level = 2
      ORDER BY c.weight DESC
    `;
    
    return subCategories.map(sub => ({
      ...sub,
      toolCount: parseInt(sub.toolCount) || 0
    }));
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    return [];
  }
}

/**
 * 通过ID查找特定二级分类
 */
export async function findSubcategoryById(id: number, locale: string): Promise<SubCategory | null> {
  try {
    const [subCategory] = await prisma.$queryRaw<any[]>`
      SELECT 
        c.id, c.slug, c.icon_url as "iconUrl", c.weight, 
        c.parent_id as "parentId", ct.name, ct.description,
        (SELECT COUNT(*) FROM tool_categories tc WHERE tc.category_id = c.id) as "toolCount"
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
      WHERE c.level = 2 AND c.id = ${id}
    `;
    
    if (!subCategory) return null;
    
    return {
      ...subCategory,
      toolCount: parseInt(subCategory.toolCount) || 0
    };
  } catch (error) {
    console.error(`Error finding subcategory ${id}:`, error);
    return null;
  }
}

/**
 * 通过slug查找特定二级分类
 */
export async function findSubcategoryBySlug(slug: string, locale: string): Promise<SubCategory | null> {
  try {
    const [subCategory] = await prisma.$queryRaw<any[]>`
      SELECT 
        c.id, c.slug, c.icon_url as "iconUrl", c.weight, 
        c.parent_id as "parentId", ct.name, ct.description,
        (SELECT COUNT(*) FROM tool_categories tc WHERE tc.category_id = c.id) as "toolCount"
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
      WHERE c.level = 2 AND c.slug = ${slug}
    `;
    
    if (!subCategory) return null;
    
    return {
      ...subCategory,
      toolCount: parseInt(subCategory.toolCount) || 0
    };
  } catch (error) {
    console.error(`Error finding subcategory ${slug}:`, error);
    return null;
  }
}

/**
 * 通过ID查找特定一级分类
 */
export async function findMainCategoryById(id: number, locale: string): Promise<MainCategory | null> {
  try {
    const [mainCategory] = await prisma.$queryRaw<any[]>`
      SELECT 
        c.id, c.slug, c.icon_url as "iconUrl", c.main_category as "mainCategory", 
        c.weight, ct.name, ct.description,
        (SELECT COUNT(*) FROM tool_categories tc WHERE tc.category_id = c.id) as "toolCount"
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
      WHERE c.level = 1 AND c.id = ${id}
    `;
    
    if (!mainCategory) return null;
    
    // 获取子分类
    const subCategories = await prisma.$queryRaw<any[]>`
      SELECT 
        c.id, c.slug, c.icon_url as "iconUrl", c.weight, 
        c.parent_id as "parentId", ct.name, ct.description,
        (SELECT COUNT(*) FROM tool_categories tc WHERE tc.category_id = c.id) as "toolCount"
      FROM categories c
      LEFT JOIN category_translations ct ON c.id = ct.category_id AND ct.locale = ${locale}
      WHERE c.level = 2 AND c.parent_id = ${id}
      ORDER BY c.weight DESC
    `;
    
    return {
      ...mainCategory,
      toolCount: parseInt(mainCategory.toolCount) || 0,
      children: subCategories.map(sub => ({
        ...sub,
        toolCount: parseInt(sub.toolCount) || 0
      }))
    };
  } catch (error) {
    console.error(`Error finding main category ${id}:`, error);
    return null;
  }
}

/**
 * 查找二级分类所属的一级分类
 */
export async function findParentCategory(subCategoryId: number, locale: string): Promise<MainCategory | null> {
  try {
    // 先查询子分类以获取父分类ID
    const [subCategory] = await prisma.$queryRaw<any[]>`
      SELECT parent_id as "parentId"
      FROM categories
      WHERE id = ${subCategoryId} AND level = 2
    `;
    
    if (!subCategory || !subCategory.parentId) return null;
    
    // 查询父分类
    return findMainCategoryById(subCategory.parentId, locale);
  } catch (error) {
    console.error(`Error finding parent category for ${subCategoryId}:`, error);
    return null;
  }
}

/**
 * 获取分类路径 [主分类, 子分类]
 */
export async function getCategoryPath(subCategoryId: number, locale: string): Promise<[MainCategory | null, SubCategory | null]> {
  try {
    // 查询子分类
    const subCategory = await findSubcategoryById(subCategoryId, locale);
    if (!subCategory) return [null, null];
    
    // 查询父分类
    const mainCategory = await findParentCategory(subCategoryId, locale);
    
    return [mainCategory, subCategory];
  } catch (error) {
    console.error(`Error getting category path for ${subCategoryId}:`, error);
    return [null, null];
  }
} 