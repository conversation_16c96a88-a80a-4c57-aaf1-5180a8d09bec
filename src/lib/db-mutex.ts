// 数据库操作互斥锁，确保同时只有一个数据库操作
class DatabaseMutex {
  private static instance: DatabaseMutex;
  private isLocked = false;
  private queue: Array<() => void> = [];

  private constructor() {}

  static getInstance(): DatabaseMutex {
    if (!DatabaseMutex.instance) {
      DatabaseMutex.instance = new DatabaseMutex();
    }
    return DatabaseMutex.instance;
  }

  async acquire(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.isLocked) {
        this.isLocked = true;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release(): void {
    if (this.queue.length > 0) {
      const next = this.queue.shift()!;
      next();
    } else {
      this.isLocked = false;
    }
  }

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    await this.acquire();
    try {
      const result = await operation();
      return result;
    } finally {
      this.release();
    }
  }
}

export const dbMutex = DatabaseMutex.getInstance();

// 简化的数据库操作包装器
export async function executeDbOperation<T>(
  operation: () => Promise<T>,
  fallback: T,
  errorMessage?: string
): Promise<T> {
  try {
    return await dbMutex.execute(operation);
  } catch (error) {
    console.error(errorMessage || 'Database operation error:', error);
    return fallback;
  }
}
