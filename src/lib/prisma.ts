import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

// 配置Prisma客户端，优化连接池设置
export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development'
    ? ['error', 'warn'] // 开发环境：移除query日志，减少噪音
    : ['error'], // 生产环境：只保留错误日志
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
})

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// 优雅关闭数据库连接
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

// 扩展类型，处理password字段问题
export type UserWithPassword = {
  password?: string;
} & Awaited<ReturnType<typeof prisma.user.findFirst>>;

/**
 * 扩展的findUser方法，支持使用password字段查询和返回
 */
export async function findUserWithPassword(email: string, provider: string) {
  // 这里使用any绕过TypeScript类型检查
  const user = await prisma.user.findFirst({
    where: {
      email,
      signinProvider: provider,
      isDeleted: false,
    } as any,
  }) as UserWithPassword;
  
  return user;
}

/**
 * 创建用户，支持password字段
 */
export async function createUserWithPassword(userData: { 
  uuid: string;
  email: string;
  password: string;
  signinProvider: string;
  nickname?: string;
}) {
  // 使用any绕过类型检查
  const user = await prisma.user.create({
    data: {
      uuid: userData.uuid,
      email: userData.email,
      password: userData.password,
      signinProvider: userData.signinProvider,
      nickname: userData.nickname,
      isDeleted: false,
    } as any,
  });
  
  return user;
}
