import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// SEO utility functions
export function generateMetaTitle(title: string, siteName: string = "Aistak"): string {
  if (title.includes(siteName)) {
    return title;
  }
  return `${title} | ${siteName}`;
}

export function generateMetaDescription(description: string, maxLength: number = 160): string {
  if (description.length <= maxLength) {
    return description;
  }
  return description.substring(0, maxLength - 3) + "...";
}

export function generateKeywords(baseKeywords: string[], additionalKeywords: string[] = []): string[] {
  const allKeywords = [...baseKeywords, ...additionalKeywords];
  // Remove duplicates and limit to 10 keywords
  return Array.from(new Set(allKeywords)).slice(0, 10);
}

export function generateCanonicalUrl(path: string, locale: string, baseUrl: string = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aistak.com'): string {
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  return `${baseUrl}/${locale}/${cleanPath}`.replace(/\/+/g, '/').replace(/\/$/, '') || baseUrl;
}

export function generateAlternateUrls(path: string, locales: string[], baseUrl: string = process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aistak.com'): Record<string, string> {
  const alternates: Record<string, string> = {};
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;

  locales.forEach(locale => {
    alternates[locale] = `${baseUrl}/${locale}/${cleanPath}`.replace(/\/+/g, '/').replace(/\/$/, '') || baseUrl;
  });

  return alternates;
}
