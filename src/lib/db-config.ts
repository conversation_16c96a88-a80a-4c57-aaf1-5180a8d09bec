// 数据库配置 - 根据环境优化
export const dbConfig = {
  // 连接池配置
  connectionPool: {
    // 生产环境更保守的连接数
    maxConnections: process.env.NODE_ENV === 'production' ? 5 : 10,
    // 连接超时时间
    connectionTimeout: process.env.NODE_ENV === 'production' ? 30000 : 20000,
    // 查询超时时间
    queryTimeout: process.env.NODE_ENV === 'production' ? 60000 : 30000,
  },

  // 缓存配置
  cache: {
    // 生产环境更长的缓存时间
    defaultTTL: process.env.NODE_ENV === 'production' ? 1800 : 600, // 30分钟 vs 10分钟
    maxSize: process.env.NODE_ENV === 'production' ? 1000 : 500,
  },

  // 日志配置
  logging: {
    // 生产环境只记录错误和警告
    level: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
    enableQueryLogging: process.env.NODE_ENV !== 'production',
    enablePerformanceLogging: process.env.NODE_ENV !== 'production',
  },

  // 性能监控
  monitoring: {
    // 慢查询阈值（毫秒）
    slowQueryThreshold: process.env.NODE_ENV === 'production' ? 5000 : 3000,
    // 是否启用性能监控
    enablePerformanceMonitoring: process.env.NODE_ENV === 'production',
  },

  // 重试配置
  retry: {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
  }
};

// 生产环境数据库URL优化
export function getOptimizedDatabaseUrl(): string {
  const baseUrl = process.env.DATABASE_URL || '';
  
  if (process.env.NODE_ENV === 'production') {
    // 生产环境连接池参数
    const params = new URLSearchParams({
      'connection_limit': '5',
      'pool_timeout': '30',
      'connect_timeout': '10',
      'sslmode': 'require', // 生产环境强制SSL
    });
    
    return baseUrl.includes('?') 
      ? `${baseUrl}&${params.toString()}`
      : `${baseUrl}?${params.toString()}`;
  }
  
  return baseUrl;
}

// 性能监控工具
export class DatabasePerformanceMonitor {
  private static queryTimes: Map<string, number[]> = new Map();
  
  static startQuery(queryId: string): () => void {
    const startTime = Date.now();
    
    return () => {
      const duration = Date.now() - startTime;
      
      // 记录查询时间
      if (!this.queryTimes.has(queryId)) {
        this.queryTimes.set(queryId, []);
      }
      this.queryTimes.get(queryId)!.push(duration);
      
      // 检查慢查询
      if (duration > dbConfig.monitoring.slowQueryThreshold) {
        console.warn(`Slow query detected: ${queryId} took ${duration}ms`);
      }
      
      // 开发环境显示查询时间
      if (dbConfig.logging.enablePerformanceLogging) {
        console.log(`Query ${queryId} completed in ${duration}ms`);
      }
    };
  }
  
  static getQueryStats(queryId: string): { avg: number; min: number; max: number; count: number } | null {
    const times = this.queryTimes.get(queryId);
    if (!times || times.length === 0) return null;
    
    return {
      avg: times.reduce((a, b) => a + b, 0) / times.length,
      min: Math.min(...times),
      max: Math.max(...times),
      count: times.length
    };
  }
  
  static getAllStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    for (const [queryId] of this.queryTimes) {
      stats[queryId] = this.getQueryStats(queryId);
    }
    return stats;
  }
  
  static clearStats(): void {
    this.queryTimes.clear();
  }
}

// 数据库健康检查配置
export const healthCheckConfig = {
  // 检查间隔（毫秒）
  interval: process.env.NODE_ENV === 'production' ? 60000 : 30000, // 1分钟 vs 30秒
  // 超时时间
  timeout: 5000,
  // 重试次数
  maxRetries: 3,
};
