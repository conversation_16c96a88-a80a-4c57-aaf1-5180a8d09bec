import { Metadata } from 'next';

// SEO Configuration Constants
export const SEO_CONFIG = {
  siteName: 'Aistak',
  siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'https://www.aistak.com',
  defaultTitle: 'Aistak - Your AI Tools Navigator',
  defaultDescription: 'Discover the best AI tools and software. Aistak helps you find AI solutions for productivity, creativity, and more.',
  defaultKeywords: ['AI tools', 'artificial intelligence', 'software', 'productivity', 'Aistak', 'AI directory', 'best AI tools'],
  twitterHandle: '@aistak',
  facebookAppId: '',
  defaultImage: '/images/og-default.jpg',
  locales: ['en', 'zh'],
  defaultLocale: 'en'
};

// Interface for SEO metadata generation
export interface SEOMetadata {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  locale?: string;
  type?: 'website' | 'article';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
}

// Generate comprehensive metadata for pages
export function generateSEOMetadata(params: SEOMetadata): Metadata {
  const {
    title,
    description,
    keywords = [],
    image,
    url,
    locale = SEO_CONFIG.defaultLocale,
    type = 'website',
    publishedTime,
    modifiedTime,
    author,
    section,
    tags = []
  } = params;

  const metaTitle = title ? `${title} | ${SEO_CONFIG.siteName}` : SEO_CONFIG.defaultTitle;
  const metaDescription = description || SEO_CONFIG.defaultDescription;
  const metaKeywords = [...SEO_CONFIG.defaultKeywords, ...keywords, ...tags].slice(0, 10);
  const metaImage = image || SEO_CONFIG.defaultImage;
  const metaUrl = url || SEO_CONFIG.siteUrl;

  const metadata: Metadata = {
    metadataBase: new URL(SEO_CONFIG.siteUrl),
    title: metaTitle,
    description: metaDescription,
    keywords: metaKeywords,
    authors: author ? [{ name: author }] : [{ name: 'Aistak Team' }],
    creator: 'Aistak Team',
    publisher: 'Aistak',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: type,
      locale: locale,
      url: metaUrl,
      title: metaTitle,
      description: metaDescription,
      siteName: SEO_CONFIG.siteName,
      images: [
        {
          url: metaImage,
          width: 1200,
          height: 630,
          alt: metaTitle,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      site: SEO_CONFIG.twitterHandle,
      creator: SEO_CONFIG.twitterHandle,
      title: metaTitle,
      description: metaDescription,
      images: [metaImage],
    },
    alternates: {
      canonical: metaUrl,
      languages: generateAlternateLanguages(url || '', locale),
    },
  };

  // Add article-specific metadata
  if (type === 'article' && (publishedTime || modifiedTime)) {
    metadata.openGraph = {
      ...metadata.openGraph,
      type: 'article',
      publishedTime,
      modifiedTime,
      authors: author ? [author] : undefined,
      section,
      tags,
    };
  }

  return metadata;
}

// Generate alternate language URLs
function generateAlternateLanguages(path: string, currentLocale: string): Record<string, string> {
  const alternates: Record<string, string> = {};
  
  SEO_CONFIG.locales.forEach(locale => {
    if (locale !== currentLocale) {
      const cleanPath = path.replace(`/${currentLocale}`, '').replace(/^\//, '');
      alternates[locale] = `${SEO_CONFIG.siteUrl}/${locale}/${cleanPath}`.replace(/\/+/g, '/').replace(/\/$/, '');
    }
  });
  
  return alternates;
}

// Generate JSON-LD structured data
export function generateStructuredData(type: string, data: any): string {
  const baseStructure = {
    '@context': 'https://schema.org',
    '@type': type,
    ...data
  };

  return JSON.stringify(baseStructure);
}

// Generate Organization structured data
export function generateOrganizationSchema(): string {
  return generateStructuredData('Organization', {
    name: SEO_CONFIG.siteName,
    url: SEO_CONFIG.siteUrl,
    logo: `${SEO_CONFIG.siteUrl}/logo.png`,
    description: SEO_CONFIG.defaultDescription,
    sameAs: [
      // Add social media URLs here
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      availableLanguage: ['English', 'Chinese']
    }
  });
}

// Generate WebSite structured data
export function generateWebsiteSchema(): string {
  return generateStructuredData('WebSite', {
    name: SEO_CONFIG.siteName,
    url: SEO_CONFIG.siteUrl,
    description: SEO_CONFIG.defaultDescription,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${SEO_CONFIG.siteUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string'
    }
  });
}

// Generate Product structured data for AI tools
export function generateToolSchema(tool: any, locale: string): string {
  const translation = tool.translations?.find((t: any) => t.locale === locale) || tool.translations?.[0];
  
  return generateStructuredData('SoftwareApplication', {
    name: translation?.name || tool.name,
    description: translation?.description || tool.description,
    url: tool.url,
    image: tool.iconUrl,
    applicationCategory: 'AI Tool',
    operatingSystem: 'Web Browser',
    offers: {
      '@type': 'Offer',
      price: tool.pricingType === 'free' ? '0' : undefined,
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock'
    },
    aggregateRating: tool.rating ? {
      '@type': 'AggregateRating',
      ratingValue: tool.rating,
      ratingCount: 1
    } : undefined,
    author: {
      '@type': 'Organization',
      name: tool.publisher || 'Unknown'
    }
  });
}

// Generate BreadcrumbList structured data
export function generateBreadcrumbSchema(breadcrumbs: Array<{name: string, url: string}>): string {
  const itemListElement = breadcrumbs.map((crumb, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: crumb.name,
    item: crumb.url
  }));

  return generateStructuredData('BreadcrumbList', {
    itemListElement
  });
}

// Generate FAQ structured data
export function generateFAQSchema(faqs: Array<{question: string, answer: string}>): string {
  const mainEntity = faqs.map(faq => ({
    '@type': 'Question',
    name: faq.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: faq.answer
    }
  }));

  return generateStructuredData('FAQPage', {
    mainEntity
  });
}

// Validate and clean meta description
export function cleanMetaDescription(description: string, maxLength: number = 160): string {
  if (!description) return '';
  
  // Remove HTML tags
  const cleanText = description.replace(/<[^>]*>/g, '');
  
  // Trim and limit length
  if (cleanText.length <= maxLength) {
    return cleanText.trim();
  }
  
  return cleanText.substring(0, maxLength - 3).trim() + '...';
}

// Generate meta keywords from content
export function extractKeywords(content: string, additionalKeywords: string[] = []): string[] {
  const baseKeywords = SEO_CONFIG.defaultKeywords;
  const contentKeywords = content
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3)
    .slice(0, 5);

  const allKeywords = [...baseKeywords, ...additionalKeywords, ...contentKeywords];
  return Array.from(new Set(allKeywords)).slice(0, 10);
}
