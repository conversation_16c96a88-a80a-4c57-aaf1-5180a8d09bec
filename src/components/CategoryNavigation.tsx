'use client';

import { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

interface Category {
  id: number;
  slug: string;
  level: number;
  translations: {
    name: string;
    locale: string;
  }[];
  _count: {
    tools: number;
  };
}

interface CategoryNavigationProps {
  locale: string;
  className?: string;
}

export default function CategoryNavigation({ locale, className }: CategoryNavigationProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [loading, setLoading] = useState(true);

  // 获取一级分类列表
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/categories?locale=${locale}&level=1`);
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [locale]);

  // 检查滚动状态
  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // 监听滚动事件
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      checkScrollButtons(); // 初始检查
      
      // 监听窗口大小变化
      const handleResize = () => {
        setTimeout(checkScrollButtons, 100);
      };
      window.addEventListener('resize', handleResize);
      
      return () => {
        container.removeEventListener('scroll', checkScrollButtons);
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [categories]);

  // 滚动函数
  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 300;
      const newScrollLeft = direction === 'left' 
        ? scrollContainerRef.current.scrollLeft - scrollAmount
        : scrollContainerRef.current.scrollLeft + scrollAmount;
      
      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  // 处理分类选择
  const handleCategorySelect = (categorySlug: string) => {
    setSelectedCategory(categorySlug);
    
    const params = new URLSearchParams(searchParams.toString());
    
    if (categorySlug) {
      params.set('category', categorySlug);
    } else {
      params.delete('category');
    }

    const queryString = params.toString();
    const newUrl = `/${locale}/tools${queryString ? `?${queryString}` : ''}`;
    
    router.push(newUrl);
  };

  if (loading) {
    return (
      <div className={cn("bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700", className)}>
        <div className="container mx-auto px-4 py-4">
          <div className="flex space-x-3">
            {[...Array(8)].map((_, index) => (
              <div
                key={index}
                className="h-10 w-24 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse flex-shrink-0"
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700", className)}>
      <div className="container mx-auto px-4 py-4">
        <div className="relative">
          {/* 左滚动按钮 */}
          {canScrollLeft && (
            <button
              onClick={() => scroll('left')}
              className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-2 shadow-md hover:shadow-lg transition-shadow"
            >
              <ChevronLeft className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </button>
          )}

          {/* 分类滚动容器 */}
          <div
            ref={scrollContainerRef}
            className="flex space-x-3 overflow-x-auto scrollbar-hide scroll-smooth px-8"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {/* 全部分类按钮 */}
            <motion.button
              onClick={() => handleCategorySelect('')}
              className={cn(
                "flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap",
                !selectedCategory
                  ? "bg-primary text-white shadow-md"
                  : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              All Tools
            </motion.button>

            {/* 分类按钮 */}
            {categories.slice(0, 15).map((category) => {
              const translation = category.translations.find(t => t.locale === locale) || category.translations[0];
              if (!translation) return null;
              
              const isSelected = selectedCategory === category.slug;
              
              return (
                <motion.button
                  key={category.slug}
                  onClick={() => handleCategorySelect(category.slug)}
                  className={cn(
                    "flex-shrink-0 px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap flex items-center space-x-2",
                    isSelected
                      ? "bg-primary text-white shadow-md"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                  )}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span>{translation.name}</span>
                  {/* <span className={cn(
                    "text-xs px-1.5 py-0.5 rounded-full",
                    isSelected 
                      ? "bg-white/20 text-white" 
                      : "bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-400"
                  )}>
                    {category._count.tools}
                  </span> */}
                </motion.button>
              );
            })}

            {/* 如果有更多分类，显示省略号 */}
            {categories.length > 15 && (
              <div className="flex-shrink-0 px-4 py-2 text-gray-400 dark:text-gray-500 text-sm">
                ...
              </div>
            )}
          </div>

          {/* 右滚动按钮 */}
          {canScrollRight && (
            <button
              onClick={() => scroll('right')}
              className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full p-2 shadow-md hover:shadow-lg transition-shadow"
            >
              <ChevronRight className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            </button>
          )}
        </div>
      </div>

      {/* 隐藏滚动条的CSS */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
}
