'use client';

import Image from 'next/image';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface SEOImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  className?: string;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
  fallbackSrc?: string;
  title?: string;
}

export function SEOImage({
  src,
  alt,
  width,
  height,
  fill = false,
  sizes,
  priority = false,
  quality = 85,
  className,
  placeholder = 'blur',
  blurDataURL,
  loading = 'lazy',
  onLoad,
  onError,
  fallbackSrc,
  title,
  ...props
}: SEOImageProps) {
  const [imageSrc, setImageSrc] = useState(src);
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Generate default blur data URL if not provided
  const defaultBlurDataURL = blurDataURL || 
    `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 ${width || 400} ${height || 300}' width='${width || 400}' height='${height || 300}'%3E%3Crect width='${width || 400}' height='${height || 300}' fill='%23f5f5f5'/%3E%3C/svg%3E`;

  const handleLoad = () => {
    setImageLoaded(true);
    onLoad?.();
  };

  const handleError = () => {
    setImageError(true);
    if (fallbackSrc && imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
      setImageError(false);
    } else {
      onError?.();
    }
  };

  // Enhanced alt text for better SEO
  const enhancedAlt = alt || 'Image';
  
  // Add structured data attributes for better SEO
  const structuredProps = {
    itemProp: 'image',
    'data-loaded': imageLoaded,
    'data-error': imageError,
  };

  const imageProps = {
    src: imageSrc,
    alt: enhancedAlt,
    title: title || enhancedAlt,
    quality,
    priority,
    loading,
    placeholder,
    blurDataURL: defaultBlurDataURL,
    onLoad: handleLoad,
    onError: handleError,
    className: cn(
      'transition-opacity duration-300',
      imageLoaded ? 'opacity-100' : 'opacity-0',
      imageError && 'opacity-50',
      className
    ),
    ...structuredProps,
    ...props,
  };

  if (fill) {
    return (
      <Image
        {...imageProps}
        fill
        sizes={sizes || '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'}
      />
    );
  }

  return (
    <Image
      {...imageProps}
      width={width}
      height={height}
      sizes={sizes}
    />
  );
}

// Specialized component for tool icons with SEO optimization
export function ToolIcon({
  tool,
  locale,
  size = 64,
  className,
  priority = false,
}: {
  tool: any;
  locale: string;
  size?: number;
  className?: string;
  priority?: boolean;
}) {
  const translation = tool.translations?.find((t: any) => t.locale === locale) || tool.translations?.[0];
  const toolName = translation?.name || tool.name || 'AI Tool';
  
  return (
    <SEOImage
      src={tool.iconUrl}
      alt={`${toolName} - AI Tool Icon`}
      width={size}
      height={size}
      quality={90}
      priority={priority}
      className={cn('rounded-lg object-contain', className)}
      fallbackSrc="/images/default-tool-icon.png"
      title={`${toolName} - AI Tool`}
    />
  );
}

// Specialized component for tool screenshots with SEO optimization
export function ToolScreenshot({
  tool,
  locale,
  className,
  priority = false,
  onLoad,
  onError,
}: {
  tool: any;
  locale: string;
  className?: string;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}) {
  const translation = tool.translations?.find((t: any) => t.locale === locale) || tool.translations?.[0];
  const toolName = translation?.name || tool.name || 'AI Tool';

  return (
    <SEOImage
      src={tool.iconUrl}
      alt={`${toolName} - AI Tool Screenshot and Interface Preview`}
      fill
      quality={85}
      priority={priority}
      className={cn('object-cover', className)}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      fallbackSrc="/images/default-tool-screenshot.png"
      title={`${toolName} - AI Tool Interface`}
      onLoad={onLoad}
      onError={onError}
    />
  );
}

// Component for category icons with SEO optimization
export function CategoryIcon({
  category,
  locale,
  size = 48,
  className,
}: {
  category: any;
  locale: string;
  size?: number;
  className?: string;
}) {
  const translation = category.translations?.find((t: any) => t.locale === locale) || category.translations?.[0];
  const categoryName = translation?.name || category.name || 'Category';
  
  return (
    <SEOImage
      src={category.iconUrl || '/images/default-category-icon.png'}
      alt={`${categoryName} - AI Tools Category Icon`}
      width={size}
      height={size}
      quality={85}
      className={cn('rounded-lg object-contain', className)}
      title={`${categoryName} - AI Tools Category`}
    />
  );
}

// Component for user avatars with SEO optimization
export function UserAvatar({
  user,
  size = 40,
  className,
}: {
  user: any;
  size?: number;
  className?: string;
}) {
  const userName = user.name || user.nickname || 'User';
  
  return (
    <SEOImage
      src={user.image || user.avatar || '/images/default-avatar.png'}
      alt={`${userName} - User Avatar`}
      width={size}
      height={size}
      quality={85}
      className={cn('rounded-full object-cover', className)}
      title={`${userName} - User Profile`}
    />
  );
}

// Component for logo with SEO optimization
export function Logo({
  size = 120,
  className,
  priority = true,
}: {
  size?: number;
  className?: string;
  priority?: boolean;
}) {
  return (
    <SEOImage
      src="/logo.png"
      alt="Aistak - AI Tools Navigator Logo"
      width={size}
      height={size}
      quality={95}
      priority={priority}
      className={cn('object-contain', className)}
      title="Aistak - Your AI Tools Navigator"
    />
  );
}
