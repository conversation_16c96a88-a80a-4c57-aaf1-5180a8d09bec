'use client';

import { useState, useRef, useEffect } from 'react';
import { Search, Loader2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Tool } from '@/app/actions';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslations } from 'next-intl';

interface SearchToolsProps {
  locale: string;
  placeholder?: string;
  searchButtonText?: string;
}

export default function SearchTools({ 
  locale,
  placeholder = '搜索AI工具...',
  searchButtonText = '搜索'
}: SearchToolsProps) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Tool[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  const t = useTranslations('tools.toolDetail');

  // 处理点击外部区域关闭下拉列表
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
        setIsFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理搜索查询
  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(searchQuery)}&locale=${locale}`);
      if (response.ok) {
        const data = await response.json();
        setResults(data.tools || []);
      } else {
        console.error('搜索失败');
        setResults([]);
      }
    } catch (error) {
      console.error('搜索错误:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    if (value.trim().length > 1) {
      handleSearch(value);
      setShowResults(true);
    } else {
      setResults([]);
      setShowResults(false);
    }
  };

  // 处理搜索表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      router.push(`/${locale}/tools?search=${encodeURIComponent(query)}`);
      setShowResults(false);
    }
  };

  // 处理输入框聚焦
  const handleFocus = () => {
    setIsFocused(true);
    if (query.trim().length > 1) {
      setShowResults(true);
    }
  };

  // 处理按键事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setShowResults(false);
      inputRef.current?.blur();
    }
  };

  return (
    <div className="relative w-full max-w-3xl mx-auto" ref={searchRef}>
      <motion.form 
        onSubmit={handleSubmit} 
        className="relative"
        initial={{ y: 10, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="relative flex items-center">
          <motion.div 
            className={cn(
              "absolute left-4 transition-colors duration-200",
              isFocused ? "text-primary" : "text-muted-foreground"
            )}
            animate={{ scale: isFocused ? 1.1 : 1 }}
            transition={{ duration: 0.2 }}
          >
            <Search size={20} />
          </motion.div>
          
          <motion.input
            ref={inputRef}
            type="text"
            value={query}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className={cn(
              "w-full h-14 pl-12 pr-32 rounded-full",
              "border bg-background/80 backdrop-blur-sm",
              "transition-all duration-300 ease-in-out",
              "focus:ring-2 focus:ring-primary focus:border-primary focus:outline-none",
              isFocused 
                ? "border-primary shadow-lg shadow-primary/10" 
                : "border-border shadow-md"
            )}
            whileFocus={{ scale: 1.02 }}
          />
          
          <motion.button
            type="submit"
            className="absolute right-2 h-10 px-6 bg-primary text-white rounded-full font-medium shadow-md hover:shadow-lg transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isLoading ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              searchButtonText
            )}
          </motion.button>
        </div>
      </motion.form>

      {/* 下拉搜索结果 */}
      <AnimatePresence>
        {showResults && (
          <motion.div 
            className="absolute z-50 w-full mt-3 bg-background/90 backdrop-blur-md rounded-xl border border-border shadow-xl max-h-[60vh] overflow-y-auto"
            initial={{ opacity: 0, y: 10, height: 0 }}
            animate={{ opacity: 1, y: 0, height: 'auto' }}
            exit={{ opacity: 0, y: 10, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            {isLoading ? (
              <div className="flex justify-center items-center p-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-3 text-muted-foreground">{placeholder.replace('...', '')}...</span>
              </div>
            ) : results.length > 0 ? (
              <motion.ul 
                className="py-2"
                initial="hidden"
                animate="visible"
                variants={{
                  visible: {
                    transition: {
                      staggerChildren: 0.05
                    }
                  }
                }}
              >
                {results.map((tool) => {
                  const translation = tool.translations.find(t => t.locale === locale) || tool.translations[0];
                  
                  return (
                    <motion.li 
                      key={tool.toolId} 
                      className="hover:bg-accent/50 transition-colors"
                      variants={{
                        hidden: { opacity: 0, y: 10 },
                        visible: { opacity: 1, y: 0 }
                      }}
                    >
                      <Link 
                        href={`/${locale}/tools/${tool.toolId}`}
                        className="flex items-center gap-4 p-4"
                        onClick={() => setShowResults(false)}
                      >
                        <div className="relative w-12 h-12 overflow-hidden rounded-lg bg-muted shrink-0 border border-border">
                          <Image
                            src={tool.iconUrl}
                            alt={translation.name}
                            fill
                            sizes="48px"
                            className="object-cover transition-opacity duration-200"
                            onError={(e) => {
                              (e.target as HTMLImageElement).style.display = 'none';
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-foreground">
                            {translation.name}
                          </p>
                          <p className="text-sm text-muted-foreground line-clamp-1 mt-1">
                            {translation.description}
                          </p>
                        </div>
                        {tool.isPremium && (
                          <span className="shrink-0 px-2 py-1 rounded-full bg-amber-100 dark:bg-amber-900/40 text-amber-800 dark:text-amber-300 text-xs font-medium">
                            {t('pricing.paid', { defaultValue: '付费' })}
                          </span>
                        )}
                      </Link>
                    </motion.li>
                  );
                })}
                <motion.li 
                  className="px-4 py-3 border-t border-border mt-2"
                  variants={{
                    hidden: { opacity: 0 },
                    visible: { opacity: 1 }
                  }}
                >
                  <Link 
                    href={`/${locale}/tools?search=${encodeURIComponent(query)}`}
                    className="flex justify-center items-center text-primary font-medium hover:underline gap-2 transition-colors"
                    onClick={() => setShowResults(false)}
                  >
                    <span>{t('viewAllResults', { defaultValue: '查看全部搜索结果' })}</span>
                    <motion.span 
                      initial={{ x: 0 }}
                      whileHover={{ x: 5 }}
                    >
                      →
                    </motion.span>
                  </Link>
                </motion.li>
              </motion.ul>
            ) : (
              <div className="p-8 text-center text-muted-foreground">
                <p>{t('noToolsFound', { defaultValue: '未找到相关工具' })}</p>
                <p className="text-sm mt-2">{t('tryChangeSearch', { defaultValue: '请尝试更改搜索词或浏览全部工具' })}</p>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 