'use client';

import { useState, useEffect, useRef } from 'react';
import { Search, Filter, X, ChevronDown, ArrowUpDown } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface Category {
  id: number;
  slug: string;
  level: number;
  translations: {
    name: string;
    locale: string;
  }[];
  _count: {
    tools: number;
  };
}

// Sort option keys
const SORT_OPTION_KEYS = [
  'default',
  'newest',
  'updated',
  'popular',
  'views',
  'rating',
  'favorites',
  'name',
];

interface ToolsFilterProps {
  locale: string;
  className?: string;
}

export default function ToolsFilter({ locale, className }: ToolsFilterProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations('tools.filter');

  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [selectedSort, setSelectedSort] = useState(searchParams.get('sort') || '');
  const [categories, setCategories] = useState<Category[]>([]);
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [loading, setLoading] = useState(false);

  // Refs for dropdown containers
  const categoryDropdownRef = useRef<HTMLDivElement>(null);
  const sortDropdownRef = useRef<HTMLDivElement>(null);

  // Handle clicking outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (categoryDropdownRef.current && !categoryDropdownRef.current.contains(event.target as Node)) {
        setShowCategoryDropdown(false);
      }
      if (sortDropdownRef.current && !sortDropdownRef.current.contains(event.target as Node)) {
        setShowSortDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Fetch categories list
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch(`/api/categories?locale=${locale}`);
        if (response.ok) {
          const data = await response.json();
          setCategories(data.categories || []);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, [locale]);

  // Handle search
  const handleSearch = () => {
    setLoading(true);
    const params = new URLSearchParams();

    if (searchQuery.trim()) {
      params.set('search', searchQuery.trim());
    }

    if (selectedCategory) {
      params.set('category', selectedCategory);
    }

    if (selectedSort) {
      params.set('sort', selectedSort);
    }

    const queryString = params.toString();
    const newUrl = `/${locale}/tools${queryString ? `?${queryString}` : ''}`;

    router.push(newUrl);
    setTimeout(() => setLoading(false), 500);
  };

  // Handle category selection
  const handleCategorySelect = (categorySlug: string) => {
    setSelectedCategory(categorySlug);
    setShowCategoryDropdown(false);

    const params = new URLSearchParams();

    if (searchQuery.trim()) {
      params.set('search', searchQuery.trim());
    }

    if (categorySlug) {
      params.set('category', categorySlug);
    }

    if (selectedSort) {
      params.set('sort', selectedSort);
    }

    const queryString = params.toString();
    const newUrl = `/${locale}/tools${queryString ? `?${queryString}` : ''}`;

    router.push(newUrl);
  };

  // Handle sort selection
  const handleSortSelect = (sortValue: string) => {
    setSelectedSort(sortValue);
    setShowSortDropdown(false);

    const params = new URLSearchParams();

    if (searchQuery.trim()) {
      params.set('search', searchQuery.trim());
    }

    if (selectedCategory) {
      params.set('category', selectedCategory);
    }

    if (sortValue) {
      params.set('sort', sortValue);
    }

    const queryString = params.toString();
    const newUrl = `/${locale}/tools${queryString ? `?${queryString}` : ''}`;

    router.push(newUrl);
  };

  // 清除筛选
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('');
    setSelectedSort('');
    router.push(`/${locale}/tools`);
  };

  // 获取选中分类的名称
  const getSelectedCategoryName = () => {
    if (!selectedCategory) return t('allCategories');

    const category = categories.find(cat => cat.slug === selectedCategory);
    if (!category) return t('allCategories');

    const translation = category.translations.find(t => t.locale === locale) || category.translations[0];
    return translation?.name || t('allCategories');
  };

  // 获取选中排序的名称
  const getSelectedSortName = () => {
    if (!selectedSort) return t('sortOptions.default');

    const sortKey = SORT_OPTION_KEYS.find(key => key === selectedSort);
    return sortKey ? t(`sortOptions.${sortKey}`) : t('sortOptions.default');
  };



  return (
    <div className={cn("bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700", className)}>
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col lg:flex-row gap-4 items-center">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder={t('searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10 pr-4 h-12 text-base rounded-full border-gray-300 focus:border-primary focus:ring-primary"
              />
            </div>
          </div>

          {/* 排序筛选 */}
          <div className="relative" ref={sortDropdownRef}>
            <Button
              variant="outline"
              onClick={() => setShowSortDropdown(!showSortDropdown)}
              className="h-12 px-6 rounded-full border-gray-300 hover:border-primary flex items-center gap-2 min-w-[140px] justify-between"
            >
              <div className="flex items-center gap-2">
                <ArrowUpDown className="w-4 h-4" />
                <span className="truncate">{getSelectedSortName()}</span>
              </div>
              <ChevronDown className={cn("w-4 h-4 transition-transform", showSortDropdown && "rotate-180")} />
            </Button>

            <AnimatePresence>
              {showSortDropdown && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full mt-2 left-0 right-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[200px]"
                >
                  <div className="p-2">
                    {SORT_OPTION_KEYS.map((sortKey) => (
                      <button
                        key={sortKey}
                        onClick={() => handleSortSelect(sortKey === 'default' ? '' : sortKey)}
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",
                          (selectedSort === sortKey || (!selectedSort && sortKey === 'default')) && "bg-primary/10 text-primary"
                        )}
                      >
                        {t(`sortOptions.${sortKey}`)}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 分类筛选 */}
          <div className="relative" ref={categoryDropdownRef}>
            <Button
              variant="outline"
              onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
              className="h-12 px-6 rounded-full border-gray-300 hover:border-primary flex items-center gap-2 min-w-[160px] justify-between"
            >
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4" />
                <span className="truncate">{getSelectedCategoryName()}</span>
              </div>
              <ChevronDown className={cn("w-4 h-4 transition-transform", showCategoryDropdown && "rotate-180")} />
            </Button>

            <AnimatePresence>
              {showCategoryDropdown && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full mt-2 left-0 right-0 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto"
                >
                  <div className="p-2">
                    <button
                      onClick={() => handleCategorySelect('')}
                      className={cn(
                        "w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors",
                        !selectedCategory && "bg-primary/10 text-primary"
                      )}
                    >
                      {t('allCategories')}
                    </button>
                    
                    {categories.map((category) => {
                      const translation = category.translations.find(t => t.locale === locale) || category.translations[0];
                      if (!translation) return null;
                      
                      return (
                        <button
                          key={category.slug}
                          onClick={() => handleCategorySelect(category.slug)}
                          className={cn(
                            "w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center justify-between",
                            selectedCategory === category.slug && "bg-primary/10 text-primary"
                          )}
                        >
                          <span>{translation.name}</span>
                          <span className="text-xs text-gray-500">({category._count.tools})</span>
                        </button>
                      );
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* 搜索按钮 */}
          <Button
            onClick={handleSearch}
            disabled={loading}
            className="h-12 px-8 rounded-full bg-primary hover:bg-primary/90"
          >
            {loading ? (
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
            ) : (
              t('searchButton')
            )}
          </Button>

          {/* 清除筛选按钮 */}
          {(searchQuery || selectedCategory || selectedSort) && (
            <Button
              variant="ghost"
              onClick={clearFilters}
              className="h-12 px-4 rounded-full text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4 mr-2" />
              {t('clearFilters')}
            </Button>
          )}
        </div>

        {/* 当前筛选状态显示 */}
        {(searchQuery || selectedCategory || selectedSort) && (
          <div className="mt-4 flex flex-wrap gap-2">
            {searchQuery && (
              <div className="bg-primary/10 text-primary px-3 py-1 rounded-full text-sm flex items-center gap-2">
                <span>{t('activeFilters.search')}: {searchQuery}</span>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    const params = new URLSearchParams();
                    if (selectedCategory) params.set('category', selectedCategory);
                    if (selectedSort) params.set('sort', selectedSort);
                    const queryString = params.toString();
                    router.push(`/${locale}/tools${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="hover:bg-primary/20 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}

            {selectedCategory && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center gap-2">
                <span>{t('activeFilters.category')}: {getSelectedCategoryName()}</span>
                <button
                  onClick={() => {
                    setSelectedCategory('');
                    const params = new URLSearchParams();
                    if (searchQuery.trim()) params.set('search', searchQuery.trim());
                    if (selectedSort) params.set('sort', selectedSort);
                    const queryString = params.toString();
                    router.push(`/${locale}/tools${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="hover:bg-blue-200 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}

            {selectedSort && (
              <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm flex items-center gap-2">
                <span>{t('activeFilters.sort')}: {getSelectedSortName()}</span>
                <button
                  onClick={() => {
                    setSelectedSort('');
                    const params = new URLSearchParams();
                    if (searchQuery.trim()) params.set('search', searchQuery.trim());
                    if (selectedCategory) params.set('category', selectedCategory);
                    const queryString = params.toString();
                    router.push(`/${locale}/tools${queryString ? `?${queryString}` : ''}`);
                  }}
                  className="hover:bg-green-200 rounded-full p-0.5"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
