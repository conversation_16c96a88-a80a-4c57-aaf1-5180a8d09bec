import createMiddleware from 'next-intl/middleware';
import { NextRequest, NextResponse } from 'next/server';
import { routing } from './i18n/routing';
import { locales, defaultLocale } from './i18n/routing';

const intlMiddleware = createMiddleware(routing);

export default function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // Static resources and special files don't need internationalization
  if (pathname === '/sitemap.xml' ||
      pathname === '/robots.txt' ||
      pathname === '/ads.txt' ||
      pathname === '/manifest.webmanifest' ||
      pathname === '/favicon.ico' ||
      pathname.startsWith('/sitemap-') ||
      pathname.startsWith('/images/') ||
      pathname.startsWith('/icons/') ||
      pathname.startsWith('/_next/') ||
      pathname.startsWith('/api/')) {
    return NextResponse.next();
  }
  
  // Handle old auth path redirects to localized paths
  if (pathname === '/auth/signin') {
    // Get callbackUrl parameter from original URL
    const callbackUrl = request.nextUrl.searchParams.get('callbackUrl');
    
    // Extract locale from callbackUrl
    let locale = defaultLocale;
    if (callbackUrl) {
      // Extract locale from callbackUrl, matching patterns like /en or /zh
      const localePattern = new RegExp(`\\/(${locales.join('|')})\/?`);
      const match = callbackUrl.match(localePattern);
      if (match && match[1]) {
        locale = match[1];
      }
    } else {
      // If no callbackUrl, try to get locale from Accept-Language header
      const acceptLanguage = request.headers.get('accept-language') || '';
      for (const loc of locales) {
        if (acceptLanguage.includes(loc)) {
          locale = loc;
          break;
        }
      }
    }

    // Redirect to localized auth path
    const newPathname = `/${locale}/auth/signin`;
    const url = new URL(newPathname, request.url);

    // Preserve all query parameters
    request.nextUrl.searchParams.forEach((value, key) => {
      url.searchParams.set(key, value);
    });
    
    return NextResponse.redirect(url);
  }
  
  return intlMiddleware(request);
}

export const config = {
  matcher: [
    // Exclude API, static resources and special files
    '/((?!api|_next/static|_next/image|favicon.ico|manifest.webmanifest|images|icons|sitemap|robots.txt|ads.txt).*)',
    // Original path matching
    '/',
    '/(zh|en)/:path*',
    '/auth/signin'
  ]
};